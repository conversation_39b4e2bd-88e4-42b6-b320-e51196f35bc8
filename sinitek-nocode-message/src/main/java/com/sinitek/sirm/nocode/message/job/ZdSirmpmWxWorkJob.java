package com.sinitek.sirm.nocode.message.job;

import com.sinitek.sirm.common.utils.IdEncryptUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.PageDataParam;
import com.sinitek.sirm.framework.frontend.support.PageDataResult;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.message.dto.SirmpmWxAccountRemoteDTO;
import com.sinitek.sirm.nocode.message.feign.ISirmpmWxService;
import com.sinitek.sirm.nocode.message.support.ZdMessageJobSyncHandler;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.wxwork.entity.WxWorkAccount;
import com.sinitek.sirm.wxwork.service.impl.WxWorkAccountServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 协力企微账号同步任务
 *
 * <AUTHOR>
 * @version 2025.0812
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
public class ZdSirmpmWxWorkJob extends ZdMessageJobSyncHandler {


    private static final int PAGE_SIZE = 1000;

    private final ISirmpmWxService sirmpmWxService;
    private final IOrgService orgService;

    @Value("${nocode.wxwork.xl.sync-type:0}")
    private Integer syncType;

    public ZdSirmpmWxWorkJob(ISirmpmWxService sirmpmWxService, ZdOrgUtil orgUtil, WxWorkAccountServiceImpl wxWorkAccountService, IOrgService orgService) {
        super(wxWorkAccountService, orgService, orgUtil);
        this.sirmpmWxService = sirmpmWxService;
        this.orgService = orgService;
    }


    /**
     * 定时任务,半个小时同步一次
     */
    @Scheduled(fixedDelay = 1800_000)
    public void synchronous() {
        if (Objects.equals(syncType, 0)) {
            synchronousAll();
        } else {
            synchronous(1);
        }
    }


    @Override
    public void synchronous(int pageIndex) {
        PageDataParam param = new PageDataParam();
        param.setPageIndex(pageIndex);
        param.setPageSize(PAGE_SIZE);
        synchronous(pageIndex, () -> sirmpmWxService.list(param), a -> {
            WxWorkAccount wxWorkAccount = new WxWorkAccount();
            wxWorkAccount.setOrgId(a.getOrgid());
            wxWorkAccount.setWxWorkId(a.getWxnum());
            return wxWorkAccount;
        });
    }

    private void synchronousAll() {
        List<String> orgList = orgService.findAllEmployees().stream().map(Employee::getId).map(IdEncryptUtil::encrypt).collect(Collectors.toList());
        RequestResult<String> result = sirmpmWxService.findByOrgIdList(orgList);
        if (Objects.nonNull(result) && result.isSuccess()) {
            String data = result.getData();
            if (StringUtils.isNotBlank(data)) {
                String decrypt = IdEncryptUtil.decrypt(data);
                if (StringUtils.isNotBlank(decrypt)) {
                    List<SirmpmWxAccountRemoteDTO> javaObjectList = JsonUtil.toJavaObjectList(decrypt, SirmpmWxAccountRemoteDTO.class);
                    PageDataResult<SirmpmWxAccountRemoteDTO> pageDataResult = new PageDataResult<>();
                    pageDataResult.setDatalist(javaObjectList);
                    pageDataResult.setTotalsize(javaObjectList.size());
                    saveBatch(pageDataResult, a -> {
                        WxWorkAccount wxWorkAccount = new WxWorkAccount();
                        wxWorkAccount.setOrgId(a.getOrgId());
                        wxWorkAccount.setWxWorkId(a.getWxWorkId());
                        return wxWorkAccount;
                    });
                }
            }
        }

    }
}
