server:
  port: 8097
  servlet:
    encoding:
      charset: utf-8
      enabled: true
      force: true
    context-path: /zhida
spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

  application:
    name: SINITEK-NOCODE-BACKEND
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: -1
      max-request-size: -1
# 属性类 com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 扫描该包下的 typehandler类，注册到Mybatis
  typeHandlersPackage: com.sinitek.data.mybatis.typehandlers
  global-config:
    # 是否关闭MP3.0自带的banner
    banner: true
    # 雪花算法使用的 worker-id、datacenter-id,集群环境下不同机器需要不同
    worker-id: 1
    datacenter-id: 1
    db-config:
      # keepGlobalFormat=true的字段会根据该值进行String.format,主要处理数据库关键字
      # '`%s`' 为mysql,  '%s' 为 oracle
      #column-format: '`%s`'
      column-format: '%s'
      #主键类型
      id-type: ASSIGN_ID
      #是否开启自定义的 IDGenerator策略
      id-customer-enable: false
      #是否开启兼容EntityName(使用Metadb_Entity表的记录进行兼容)
      compatible-entityname-enable: true
      #更新的时候是否判断设置为null,默认是跳过 null的更新的。现在设置为 null 也是可以更新。
      update-strategy: ignored
  type-enums-package: com.sinitek.sirm.nocode.*.enumerate

feign:
  hystrix:
    enabled: true

management:
  endpoints:
    shutdown:
      enabled: true
    web:
      exposure:
        include: "*"    #开放全部监控端点
  health:
    redis:
      enabled: false
    elasticsearch:
      enabled: false

## 是否开启子服务会话验证,先关闭
sirm:
  cloud:
    enable: true
sinicube:
  tempdir:
    auto-clean-enabled: false
  # 属性 com.sinitek.sirm.i18n.properties.I18nProperties
  # 配置类 com.sinitek.sirm.config.SpringMessageConfig
  i18n:
    # 是否开启国际化
    enable: false
    # 后端加载的message
    #basename: classpath:message/messages-common
    # 默认的message编码
    defaultEncoding: UTF-8
    # code找不到对应的message时,是否默认使用code返回
    useCodeAsDefaultMessage: true
    # 默认的语言环境
    defaultLocale: zh_CN
    # 系统支持的语言环境
    supportedLocaleInfoList:
      - locale: zh_CN
        localeName: 简体中文
        localeChineseName: 简体中文
      - locale: zh_HK
        localeName: 繁體中文
        localeChineseName: 繁体中文
    # 语言环境切换时统一使用的headerName
    headerName: lang



hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 10000

ribbon:
  ConnectTimeout: 3000
  ReadTimeout: 6000

## 指定临时目录地址为本地
localsettingpriority: true
setting:
  tempdir: ${java.io.tmpdir}
