package com.sinitek.sirm.nocode.form.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCreateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaUpdateParamDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Schema缓存服务并发测试
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("local")
public class ZdPageFormInstanceSchemaServiceConcurrencyTest {

    @Autowired
    private IZdPageFormInstanceSchemaService schemaService;

    /**
     * 测试并发创建Schema缓存
     */
    @Test
    public void testConcurrentCreateSchemaCache() throws InterruptedException {
        int threadCount = 10;
        int operationsPerThread = 5;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        List<String> createdSchemaIds = new ArrayList<>();

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        try {
                            ZdFormInstanceSchemaCreateParamDTO param = createTestCreateParam(
                                threadIndex, j);
                            String schemaId = schemaService.createSchemaCache(param);

                            synchronized (createdSchemaIds) {
                                createdSchemaIds.add(schemaId);
                            }
                            successCount.incrementAndGet();
                            log.info("线程 {} 成功创建Schema: {}", threadIndex, schemaId);
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                            log.error("线程 {} 创建Schema失败", threadIndex, e);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(30, TimeUnit.SECONDS), "并发创建测试超时");
        executor.shutdown();

        log.info("并发创建测试完成 - 成功: {}, 失败: {}, 总创建数: {}",
            successCount.get(), failureCount.get(), createdSchemaIds.size());

        // 验证结果
        assertEquals(threadCount * operationsPerThread, successCount.get() + failureCount.get());
        assertTrue(successCount.get() > 0, "至少应有一些成功的创建操作");

        // 验证Schema ID列表的一致性
        List<String> cachedSchemaIds = schemaService.findCachedSchemaId();
        assertTrue(cachedSchemaIds.containsAll(createdSchemaIds),
            "缓存的Schema ID列表应包含所有创建的ID");

        // 清理测试数据
        schemaService.deleteSchemaCacheBatch(createdSchemaIds);
    }

    /**
     * 测试并发更新Schema缓存
     */
    @Test
    public void testConcurrentUpdateSchemaCache() throws InterruptedException {
        // 先创建一个Schema用于测试
        ZdFormInstanceSchemaCreateParamDTO createParam = createTestCreateParam(0, 0);
        String schemaId = schemaService.createSchemaCache(createParam);

        int threadCount = 8;
        int operationsPerThread = 3;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        try {
                            ZdFormInstanceSchemaUpdateParamDTO updateParam = createTestUpdateParam(
                                schemaId, threadIndex, j);
                            schemaService.updateSchemaCache(updateParam);
                            successCount.incrementAndGet();
                            log.info("线程 {} 成功更新Schema: {}", threadIndex, schemaId);

                            // 短暂休眠以增加并发冲突的可能性
                            Thread.sleep(10);
                        } catch (Exception e) {
                            failureCount.incrementAndGet();
                            log.error("线程 {} 更新Schema失败", threadIndex, e);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        assertTrue(latch.await(30, TimeUnit.SECONDS), "并发更新测试超时");
        executor.shutdown();

        log.info("并发更新测试完成 - 成功: {}, 失败: {}", successCount.get(), failureCount.get());

        // 验证结果
        assertEquals(threadCount * operationsPerThread, successCount.get() + failureCount.get());
        assertTrue(successCount.get() > 0, "至少应有一些成功的更新操作");

        // 清理测试数据
        schemaService.deleteSchemaCache(schemaId);
    }

    /**
     * 测试混合并发操作（创建、更新、删除）
     */
    @Test
    public void testMixedConcurrentOperations() throws InterruptedException {
        int threadCount = 12;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 创建操作线程
        for (int i = 0; i < 4; i++) {
            final int threadIndex = i;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    for (int j = 0; j < 3; j++) {
                        ZdFormInstanceSchemaCreateParamDTO param = createTestCreateParam(
                            threadIndex, j);
                        String schemaId = schemaService.createSchemaCache(param);
                        log.info("创建线程 {} 成功创建Schema: {}", threadIndex, schemaId);
                        Thread.sleep(50);
                    }
                } catch (Exception e) {
                    log.error("创建线程 {} 执行失败", threadIndex, e);
                } finally {
                    latch.countDown();
                }
            }, executor);
            futures.add(future);
        }

        // 等待一些Schema被创建
        Thread.sleep(200);

        // 更新和删除操作线程
        for (int i = 0; i < 8; i++) {
            final int threadIndex = i + 4;
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    List<String> cachedIds = schemaService.findCachedSchemaId();
                    if (!cachedIds.isEmpty()) {
                        String schemaId = cachedIds.get(0);

                        if (threadIndex % 2 == 0) {
                            // 更新操作
                            ZdFormInstanceSchemaUpdateParamDTO updateParam = createTestUpdateParam(
                                schemaId, threadIndex, 0);
                            schemaService.updateSchemaCache(updateParam);
                            log.info("更新线程 {} 成功更新Schema: {}", threadIndex, schemaId);
                        } else {
                            // 删除操作
                            schemaService.deleteSchemaCache(schemaId);
                            log.info("删除线程 {} 成功删除Schema: {}", threadIndex, schemaId);
                        }
                    }
                } catch (Exception e) {
                    log.error("操作线程 {} 执行失败", threadIndex, e);
                } finally {
                    latch.countDown();
                }
            }, executor);
            futures.add(future);
        }

        assertTrue(latch.await(60, TimeUnit.SECONDS), "混合并发测试超时");

        // 等待所有异步操作完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        executor.shutdown();
        log.info("混合并发测试完成");

        // 清理剩余的测试数据
        List<String> remainingIds = schemaService.findCachedSchemaId();
        if (!remainingIds.isEmpty()) {
            schemaService.deleteSchemaCacheBatch(remainingIds);
        }
    }

    /**
     * 创建测试用的创建参数
     */
    private ZdFormInstanceSchemaCreateParamDTO createTestCreateParam(int threadIndex,
        int operationIndex) {
        String testSchema = String.format("{\n"
                + "                \"id\": \"test_form_%d_%d\",\n"
                + "                \"children\": [\n"
                + "                    {\n"
                + "                        \"id\": \"field_%d_%d\",\n"
                + "                        \"ref\": \"TestField_%d_%d\",\n"
                + "                        \"componentName\": \"ZDInput\",\n"
                + "                        \"props\": {\n"
                + "                            \"label\": \"测试字段_%d_%d\",\n"
                + "                            \"placeholder\": \"请输入内容\"\n"
                + "                        }\n"
                + "                    }\n"
                + "                ]\n"
                + "            }", threadIndex, operationIndex, threadIndex, operationIndex,
            threadIndex, operationIndex, threadIndex, operationIndex);

        ZdFormInstanceSchemaCreateParamDTO param = new ZdFormInstanceSchemaCreateParamDTO();
        param.setSchema(Base64.getEncoder().encodeToString(testSchema.getBytes()));
        return param;
    }

    /**
     * 创建测试用的更新参数
     */
    private ZdFormInstanceSchemaUpdateParamDTO createTestUpdateParam(String schemaId,
        int threadIndex, int operationIndex) {
        String testDiff = String.format("[\n"
                + "                    {\n"
                + "                        \"type\": \"component\",\n"
                + "                        \"data\": {\n"
                + "                            \"ref\": \"TestField_%d_%d\",\n"
                + "                            \"props\": {\n"
                + "                                \"label\": \"更新后的字段_%d_%d_%d\",\n"
                + "                                \"state\": \"normal\"\n"
                + "                            }\n"
                + "                        }\n"
                + "                    }\n"
                + "                ]", threadIndex, operationIndex, threadIndex, operationIndex,
            System.currentTimeMillis() % 1000);

        ZdFormInstanceSchemaUpdateParamDTO param = new ZdFormInstanceSchemaUpdateParamDTO();
        param.setSchemaId(schemaId);
        param.setDiff(Base64.getEncoder().encodeToString(testDiff.getBytes()));
        return param;
    }
}
