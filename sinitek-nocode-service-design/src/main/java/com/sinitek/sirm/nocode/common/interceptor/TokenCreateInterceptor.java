package com.sinitek.sirm.nocode.common.interceptor;

import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.cloud.base.support.CurrentUser;
import com.sinitek.cloud.base.support.TokenCheckInterceptor;
import com.sinitek.cloud.common.dto.UserDTO;
import com.sinitek.sirm.application.support.ApplicationOpenApiSupport;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.i18n.interceptor.LocaleChangeInterceptor;
import com.sinitek.sirm.nocode.common.utils.TokenFilterUtil;
import com.sinitek.spirit.um.server.shiro.IgnoreUrlUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0519
 * @description 当前用户信息拦截器, 仅仅本地调试用
 * @since 1.0.0-SNAPSHOT
 */

@Slf4j
@Order(Integer.MIN_VALUE)
@ConditionalOnProperty(prefix = "nocode", name = "local-authentication", havingValue = "true")
@RequiredArgsConstructor
@Configuration
public class TokenCreateInterceptor implements HandlerInterceptor, WebMvcConfigurer, SmartInitializingSingleton {


    @Resource
    private TokenCheckInterceptor tokenCheckInterceptor;

    private final ApplicationOpenApiSupport applicationOpenApiSupport;
    private final IgnoreUrlUtil ignoreUrlUtil;
    private final TokenFilterUtil tokenFilterUtil;


    @Value("${sirm.security.path:/**}")
    private String path;
    @Value("${sirm.security.excludepath:}")
    private String excludepath;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            boolean isOpenApi = this.applicationOpenApiSupport.isOpenApiAndInternalIdentification(handlerMethod);
            if (isOpenApi) {
                log.info("TokenCreateInterceptor:isOpenApi = true");
                return true;
            }
        } else {
            // 资源的话，不拦截
            return true;
        }
        String accessToken = this.tokenFilterUtil.getTokenByRequest(request);
        log.info("TokenCreateInterceptor:accessToken = {}", accessToken);
        // 不再做用户验证
        if (StringUtils.isBlank(accessToken)) {
            return fail(response);
        }
        String ignoreUpdateLastTimeHeaderValue = this.tokenFilterUtil.checkIgnoreUpdateLastTimeUrl(path);
        String clientType = this.tokenFilterUtil.getClientType(request);
        String cookie = TokenFilterUtil.getFirstHeader(request, "Cookie");
        String authorization = TokenFilterUtil.getFirstHeader(request, "Authorization");
        UserDTO user = this.tokenFilterUtil.checkToken(accessToken, clientType, ignoreUpdateLastTimeHeaderValue, cookie, authorization);
        if (Objects.nonNull(user)) {
            String responseAccessToken = user.getResponseAccessToken();
            if (StringUtils.isNotBlank(responseAccessToken) && !Objects.equals(responseAccessToken, accessToken)) {
                response.setHeader("accesstoken", responseAccessToken);
            }
            log.debug("current user = {}", user);
            CurrentUser.begin();
            CurrentUser.setRequest(request);
            CurrentUser.init(user);
            return true;
        } else {
            return fail(response);
        }
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        CurrentUser.end();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        LocaleChangeInterceptor localeChangeInterceptor = SpringFactory.getBean(LocaleChangeInterceptor.class);
        if (localeChangeInterceptor != null) {
            registry.addInterceptor(localeChangeInterceptor).order(21).addPathPatterns("/**");
        }

        List<String> ignoreUrlList = this.ignoreUrlUtil.getIgnoreUrlList();
        InterceptorRegistration registration = registry.addInterceptor(this);
        registration.addPathPatterns(org.apache.commons.lang.StringUtils.split(this.path, ","));
        registration.excludePathPatterns(ignoreUrlList);
        if (org.apache.commons.lang.StringUtils.isNotBlank(this.excludepath)) {
            registration.excludePathPatterns(org.apache.commons.lang.StringUtils.split(this.excludepath, ","));
        }

    }

    private boolean fail(HttpServletResponse response) throws Exception {
        RequestResult<Object> requestResult = RequestResult.fail("0000066");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json");
        ObjectMapper mapper = new ObjectMapper();
        String subject = mapper.writeValueAsString(requestResult);
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.getWriter().println(subject);

        return false;
    }

    @Override
    public void afterSingletonsInstantiated() {
        // 让本拦截器接管  tokenCheckInterceptor
        ReflectUtil.setFieldValue(tokenCheckInterceptor, "inCloud", "false");
    }
}
