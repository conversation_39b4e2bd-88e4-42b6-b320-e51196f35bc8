package com.sinitek.sirm.nocode.page.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.sirm.nocode.page.dao.ZdPageFavoritesDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageFavoritesDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPageFavorites;
import com.sinitek.sirm.nocode.page.mapper.ZdPageFavoritesMapper;
import com.sinitek.sirm.nocode.page.mapstruct.ZdPageFavoritesMapstruct;
import com.sinitek.sirm.nocode.page.service.IZdPageFavoritesService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 页面收藏服务实现
 *
 * <AUTHOR>
 * @version 2025.0719
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZdPageFavoritesServiceImpl extends BaseDAO<ZdPageFavoritesMapper, ZdPageFavorites, ZdPageFavoritesDAO> implements IZdPageFavoritesService {
    private final ZdPageFavoritesMapstruct mapstruct;

    @Override
    public boolean saveOrUpdate(ZdPageFavoritesDTO dto) {
        LamWrapper<ZdPageFavorites> query = LamWrapper
                .eqOrIn(ZdPageFavorites::getPageCode, dto.getFormCode())
                .eq(ZdPageFavorites::getOrgId, dto.getOrgId());
        if (exists(query)) {
            // 修改收藏时间
            LambdaUpdateWrapper<ZdPageFavorites> set = query.re(Wrappers::lambdaUpdate)
                    .set(ZdPageFavorites::getUpdateTimeStamp, new Date());
            return dao.update(set);
        } else {
            return dao.save(mapstruct.toEntity(dto));
        }
    }

    @Override
    public boolean delete(String pageCode, String orgId) {
        LambdaQueryWrapper<ZdPageFavorites> remove = eqOrIn(ZdPageFavorites::getPageCode, pageCode)
                .eq(ZdPageFavorites::getOrgId, orgId);
        return dao.remove(remove);
    }
}
