package com.sinitek.sirm.nocode.common.controller;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.NumberUtil;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.IdEncryptUtil;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.support.FrontendResponseSupport;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 文件接口
 * 需要使用显示图片的功能的话，{@link FrontendResponseSupport#conversionUrl} 会自动将 流转换为{@link com.sinitek.sirm.framework.frontend.support.RequestResult} 所以需要配置
 * conversionUrl为特定的地址做转换器，不然无法获取到图片
 *
 * <AUTHOR>
 * @version 2025.0815
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Api(value = "/frontend/api/nocode/file", tags = "零代码文件接口")
@RequestMapping("/frontend/api/nocode/file")
@RequiredArgsConstructor
@Controller

public class ZdFileController {

    private final IAttachmentExtService attachmentExtService;

    private static final int CACHE_MAX_AGE_DAYS = 365;

    @GetMapping("/pic/view/by-id")
    public ResponseEntity<Resource> pictureViewById(
            @RequestParam("id") String id
    ) {
        try {
            boolean number = NumberUtil.isNumber(id);
            long attachmentId;
            if (number) {
                attachmentId = NumberUtil.parseLong(id);
            } else {
                attachmentId = NumberUtil.parseLong(IdEncryptUtil.decrypt(id));
            }
            InputStream inputStream = attachmentExtService.getAttachmentAsInputStream(attachmentId);
            byte[] byteArray = IOUtil.toByteArray(inputStream);
            ByteArrayInputStream byteArrayInputStream = IoUtil.toStream(byteArray);
            String type = FileTypeUtil.getType(byteArrayInputStream);
            byteArrayInputStream.reset();
            MediaType mediaType = new MediaType("image", type);
            return ResponseEntity.ok()
                    .cacheControl(CacheControl
                            .maxAge(CACHE_MAX_AGE_DAYS, TimeUnit.DAYS)
                            .cachePublic()
                    )
                    .contentType(mediaType)
                    .body(new ByteArrayResource(byteArray));
        } catch (Exception e) {
            log.error("图片加载失败:{}", e.getMessage(), e);
            Resource resource = new ClassPathResource("/META-INF/resources/transparent.png");
            return ResponseEntity.ok()
                    .cacheControl(CacheControl.noCache()
                    )
                    .contentType(MediaType.IMAGE_PNG)
                    .body(resource);
        }
    }

    @GetMapping("/pic/view")
    public ResponseEntity<Resource> pictureView(
            @RequestParam("sourceId") Long sourceId,
            @RequestParam("sourceEntity") String sourceEntity,
            @RequestParam(value = "type", defaultValue = "0") Integer type

    ) {
        AttachmentDTO attachment = attachmentExtService.getAttachment(sourceEntity, sourceId, type);
        Long id = 0L;
        if (Objects.nonNull(attachment)) {
            String idStr = attachment.getId();
            String decrypt = IdEncryptUtil.decrypt(idStr);
            id = NumberTool.safeToLong(decrypt, 0L);
        }
        ResponseEntity<Resource> response = pictureViewById(id + "");
        HttpHeaders headers = response.getHeaders();
        MediaType mediaType = headers.getContentType();
        if (Objects.isNull(mediaType)) {
            mediaType = MediaType.IMAGE_PNG;
        }
        return ResponseEntity.ok()
                .cacheControl(CacheControl.noCache()
                )
                .contentType(mediaType)
                .body(response.getBody());

    }


}
