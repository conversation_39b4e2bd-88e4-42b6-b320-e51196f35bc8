package com.sinitek.sirm.nocode.page.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sinitek.data.model.tree.enumerate.TypeEnum;
import com.sinitek.sirm.common.attachment.dto.AttachmentDTO;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.component.dto.XnSelectDTO;
import com.sinitek.sirm.common.event.support.SiniCubeEventPublisher;
import com.sinitek.sirm.common.utils.IdEncryptUtil;
import com.sinitek.sirm.common.utils.IdUtil;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.dto.UploadDTO;
import com.sinitek.sirm.framework.frontend.dto.UploadFileDTO;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.dto.ZdAppSettingDTO;
import com.sinitek.sirm.nocode.app.entity.ZdApp;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.app.support.IZdAppSettingCustomizer;
import com.sinitek.sirm.nocode.common.dto.ZdOptionDTO;
import com.sinitek.sirm.nocode.common.dto.ZdTreeDTO;
import com.sinitek.sirm.nocode.common.tree.TreeDataMaker;
import com.sinitek.sirm.nocode.common.utils.CodeCreateUtil;
import com.sinitek.sirm.nocode.common.utils.ConvertUtil;
import com.sinitek.sirm.nocode.common.utils.CopyUtil;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.page.constant.PageConstant;
import com.sinitek.sirm.nocode.page.dao.ZdPageDAO;
import com.sinitek.sirm.nocode.page.dto.ZdPageCodeAndAppCodeDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageDeleteEventSourceDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPagePublishDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSaveResultDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSquareDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageSquareSearchParamDTO;
import com.sinitek.sirm.nocode.page.dto.ZdPageUpdateNameDTO;
import com.sinitek.sirm.nocode.page.entity.ZdPage;
import com.sinitek.sirm.nocode.page.enumerate.CustomUrlTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PagePublishTypeEnum;
import com.sinitek.sirm.nocode.page.enumerate.PageTypeEnum;
import com.sinitek.sirm.nocode.page.event.PageCreateEvent;
import com.sinitek.sirm.nocode.page.event.PageDeleteEvent;
import com.sinitek.sirm.nocode.page.mapper.ZdPageMapper;
import com.sinitek.sirm.nocode.page.mapstruct.ZdPageMapstruct;
import com.sinitek.sirm.nocode.page.po.ZdPageSquarePO;
import com.sinitek.sirm.nocode.page.service.IZdPageService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025-03-12 10:00:09
 * @description 针对表【zd_page(页面表)】的数据库操作Service实现
 */
@Service
@RequiredArgsConstructor
public class ZdPageServiceImpl extends BaseDAO<ZdPageMapper, ZdPage, ZdPageDAO>
        implements IZdPageService, IZdAppSettingCustomizer {

    private final ZdPageMapstruct mapstruct;
    private final ZdOrgUtil orgUtil;
    private final IAttachmentExtService attachmentExtService;
    private final SiniCubeEventPublisher eventPublisher;

    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_PAGE + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<String> appCodeList = appDeleteEvent.getCodeList();
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCodeList).select(ZdPage::getCode);
        List<String> pageCodeList = dao.list(queryWrapper).stream().map(ZdPage::getCode).collect(Collectors.toList());
        delete(queryWrapper, pageCodeList);
    }

    /**
     * 删除页面
     *
     * @param queryWrapper 删除条件
     * @param pageCodeList codeList
     * @return 是否成功
     */
    private boolean delete(LambdaQueryWrapper<ZdPage> queryWrapper, List<String> pageCodeList) {
        if (CollectionUtils.isNotEmpty(pageCodeList)) {
            SpringUtil.publishEvent(new AppDeleteEvent(AppConstant.DE_FORM, pageCodeList));
            this.eventPublisher.publishEvent(new PageDeleteEvent(
                    ZdPageDeleteEventSourceDTO
                            .builder()
                            .codes(pageCodeList)
                            .build()));
        }
        // 附件删除
        if (CollectionUtils.isNotEmpty(pageCodeList)) {
            pageCodeList.forEach(code -> {
                Long id = getIdByCode(code);
                if (id != null) {
                    attachmentExtService.removeAttachment(PageConstant.TABLE_NAME, id);
                }
            });
        }
        // 删除页面
        return dao.remove(queryWrapper);
    }


    @Override
    public void customize(ZdAppSettingDTO zdAppSettingDTO, String appCode) {

    }


    @Override
    public boolean updateName(ZdPageUpdateNameDTO zdPageUpdateNameDTO) {
        String code = zdPageUpdateNameDTO.getCode();
        String name = zdPageUpdateNameDTO.getName();
        LambdaUpdateWrapper<ZdPage> updateWrapper = lu().set(StringUtils.isNotBlank(name), ZdPage::getName, name)
                .eq(ZdPage::getCode, code);
        return dao.update(updateWrapper);
    }

    @Override
    public String getNameByCode(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code).select(ZdPage::getName);
        ZdPage zdPage = dao.getOne(queryWrapper);
        if (Objects.nonNull(zdPage)) {
            return zdPage.getName();
        } else {
            return "";
        }
    }

    @Override
    public PageTypeEnum getPageTypeByCode(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code).select(ZdPage::getPageType);
        ZdPage zdPage = dao.getOne(queryWrapper);
        if (Objects.nonNull(zdPage)) {
            return zdPage.getPageType();
        }
        return null;
    }

    @Override
    public ZdPagePublishDTO getUrlByCode(String code) {
        ZdPagePublishDTO zdPageCustomUrlDTO = new ZdPagePublishDTO();
        zdPageCustomUrlDTO.setCode(code);
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code)
                .select(ZdPage::getId, ZdPage::getUrl, ZdPage::getPublishType);
        ZdPage zdPage = dao.getOne(queryWrapper);
        if (Objects.nonNull(zdPage)) {
            zdPageCustomUrlDTO.setUrl(zdPage.getUrl());
            zdPageCustomUrlDTO.setId(zdPage.getId());
            zdPageCustomUrlDTO.setPublishToSquare(Objects.equals(zdPage.getPublishType(), PagePublishTypeEnum.SQUARE));
        }
        return zdPageCustomUrlDTO;
    }

    @Override
    public ZdPageCodeAndAppCodeDTO getByUrl(String url, CustomUrlTypeEnum type) {
        ZdPageCodeAndAppCodeDTO info = new ZdPageCodeAndAppCodeDTO();
        if (Objects.equals(type, CustomUrlTypeEnum.FORM)) {
            LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getUrl, url).select(ZdPage::getCode, ZdPage::getAppCode);
            ZdPage one = dao.getOne(queryWrapper);
            if (Objects.nonNull(one)) {
                info.setAppCode(one.getAppCode());
                info.setFormCode(one.getCode());
            }
        } else if (Objects.equals(type, CustomUrlTypeEnum.APP)) {
            LamWrapper<ZdApp> queryWrapper = LamWrapper.eqOrIn(ZdApp::getUrl, url).select(ZdApp::getCode);
            info.setAppCode(stringValue(queryWrapper));
        }
        return info;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZdPageSaveResultDTO savePage(ZdPageDTO pageDTO) {
        ZdPage zdPage = mapstruct.toEntity(pageDTO);
        // 设置code
        zdPage.setCode(CodeCreateUtil.createCode("page_"));
        Long parentId = zdPage.getParentId();
        ZdPage preNode = null;
        if (IdUtil.isDataId(parentId)) {
            preNode = dao.getById(parentId);
        }
        dao.addNode(preNode, zdPage, TypeEnum.SUB);
        // 发出创建页面事件
        pageDTO.setId(zdPage.getId());
        pageDTO.setCode(zdPage.getCode());
        pageDTO.setSort(zdPage.getSort());
        SpringUtil.publishEvent(new PageCreateEvent(pageDTO));
        return CopyUtil.copyProperties(zdPage, new ZdPageSaveResultDTO());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByCode(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code).select(ZdPage::getId, ZdPage::getPageType);
        ZdPage one = dao.getOne(queryWrapper);
        //
        boolean groupHasForm = false;
        if (Objects.nonNull(one) && PageTypeEnum.GROUP_PAGE.equals(one.getPageType())) {
            Long id = one.getId();
            groupHasForm = exists(ZdPage::getParentId, id);
        }
        if (groupHasForm) {
            throw new BussinessException("3000016");
        }
        return delete(queryWrapper, Collections.singletonList(code));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteByCode(List<String> codeList) {
        codeList.forEach(this::deleteByCode);
        return true;
    }

    @Override
    public boolean exists(String code) {
        LambdaQueryWrapper<ZdPage> queryWrapper = codeQuery(code);
        return dao.count(queryWrapper) > 0;
    }

    @Override
    public List<ZdPageDTO> listTree(String appCode, Integer type) {
        Predicate<ZdTreeDTO<ZdPageDTO>> predicate = null;
        if (Objects.equals(type, 1)) {
            predicate = ZdPageServiceImpl::filterEmptyGroup;
        }
        List<ZdPageDTO> list = mapstruct.toDTOList(dao.getBaseMapper().listTree(appCode, false));
        return TreeDataMaker.tree(list, predicate);
    }

    @Override
    public List<ZdPageDTO> publishedListTree(String appCode, Integer type) {
        List<ZdPage> pageList = dao.getBaseMapper().listTree(appCode, true)
                .stream().filter(page -> Objects.equals(page.getPageType(), PageTypeEnum.GROUP_PAGE) || (BooleanUtils.isTrue(page.getFormPublishFlag())))
                .collect(Collectors.toList());
        List<ZdPageDTO> list = mapstruct.toDTOList(pageList);
        return TreeDataMaker.tree(list, ZdPageServiceImpl::filterEmptyGroup);
    }

    private void findParent(List<ZdPage> pageList, Map<Long, ZdPage> parentMap) {
        if (CollectionUtils.isEmpty(pageList)) {
            return;
        }
        Set<Long> parentIdSet = pageList.stream()
                .map(ZdPage::getParentId).filter(parentId -> {
                    ZdPage zdPage = parentMap.get(parentId);
                    return !Objects.equals(parentId, 0L) && Objects.isNull(zdPage);
                })
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(parentIdSet)) {
            List<ZdPage> parentList = dao.listByIds(parentIdSet);
            parentList.forEach(parent -> parentMap.put(parent.getId(), parent));
            findParent(parentList, parentMap);
        }


    }

    @Override
    public String getDefaultFormCode(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = order(appCodeQuery(appCode));
        LamWrapper<ZdPage> one = LamWrapper.ins(queryWrapper)
                .select(ZdPage::getCode)
                .ne(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .one();
        String code = stringValue(one);
        if (Objects.isNull(code)) {
            return "";
        }
        return code;
    }

    @Override
    public List<XnSelectDTO> findAllForm(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = order(appCodeQuery(appCode)).select(ZdPage::getCode, ZdPage::getName);
        queryWrapper.ne(ZdPage::getPageType, 9);
        List<ZdPage> list = dao.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .map(zdPage -> {
                        XnSelectDTO xnSelectDTO = new XnSelectDTO();
                        xnSelectDTO.setValue(zdPage.getCode());
                        xnSelectDTO.setLabel(zdPage.getName());
                        return xnSelectDTO;
                    }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private static boolean filterEmptyGroup(ZdTreeDTO<ZdPageDTO> r) {
        ZdPageDTO a = r.getTreeOrgData();
        PageTypeEnum pageType = a.getPageType();
        if (Objects.equals(pageType, PageTypeEnum.GROUP_PAGE)) {
            List<ZdTreeDTO<ZdPageDTO>> children = r.getChildren();
            if (CollectionUtils.isEmpty(children)) {
                return false;
            } else {
                return children.stream().anyMatch(ZdPageServiceImpl::filterEmptyGroup);
            }
        }
        return true;
    }

    @SneakyThrows
    @Override
    public List<ZdPageDTO> move(Long moveId, Long targetId, Integer type) {
        ZdPage moveNode = idCheck(moveId);
        ZdPage targetNode = idCheck(targetId);
        if (!PageTypeEnum.GROUP_PAGE.getValue().equals(targetNode.getPageType().getValue()) && type == 2) {
            //平级表单移动是被允许的
            throw new Exception("操作有误，无法移动至表单！");
        }
        if (!Objects.equals(moveNode.getAppCode(), targetNode.getAppCode())) {
            // todo 必须是同一个应用下才可以移动
            throw new BussinessException("");
        }
        TypeEnum typeEnum = TypeEnum.fromValue(type);
        dao.moveNode(moveNode, targetNode, typeEnum);
        return listTree(moveNode.getAppCode(), 0);
    }

    @Override
    public String getAppCodeByCode(List<String> pageCodeList) {
        LambdaQueryWrapper<ZdPage> eq = codeQuery(pageCodeList).select(ZdPage::getAppCode);
        List<ZdPage> pageList = dao.list(eq);
        if (CollectionUtils.isNotEmpty(pageList)) {
            // 需要去重
            return pageList.stream().map(ZdPage::getAppCode).distinct().collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public String getAppCodeByCode(String pageCode) {
        return getAppCodeByCode(Collections.singletonList(pageCode));
    }

    @Override
    public String getAppCodeById(Long id) {
        LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getId, id)
                .select(ZdPage::getAppCode);
        ZdPage one = dao.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            return one.getAppCode();
        }
        return null;
    }

    @Override
    public void savePageCustomUrl(ZdPagePublishDTO pageCustomUrl, String orgId) {
        List<ZdPagePublishDTO> pageCustomUrlList = Collections.singletonList(pageCustomUrl);
        check(pageCustomUrlList);
        pageCustomUrlList.forEach(pageCustomUrlDTO -> {

            Boolean publishToSquare = pageCustomUrlDTO.getPublishToSquare();
            PagePublishTypeEnum publishType = null;
            if (BooleanUtils.isTrue(publishToSquare)) {
                publishType = PagePublishTypeEnum.SQUARE;
            }

            // 附件上传
            Long attachmentId = null;
            UploadDTO uploadDTO = pageCustomUrlDTO.getUploadDTO();
            if (Objects.nonNull(uploadDTO)) {
                List<UploadFileDTO> uploadFileList = uploadDTO.getUploadFileList();
                List<UploadFileDTO> removeFileList = uploadDTO.getRemoveFileList();
                if (CollectionUtils.isNotEmpty(uploadFileList) || CollectionUtils.isNotEmpty(removeFileList)) {
                    Long sourceId = getIdByCode(pageCustomUrlDTO.getCode());
                    attachmentExtService.saveAvatar(uploadDTO, sourceId, PageConstant.TABLE_NAME);
                    AttachmentDTO attachment = attachmentExtService.getAttachment(PageConstant.TABLE_NAME, sourceId, 0);
                    if (Objects.nonNull(attachment)) {
                        String id = attachment.getId();
                        attachmentId = NumberTool.safeToLong(IdEncryptUtil.decrypt(id), null);
                    }
                }
            }
            dao.update(lu()
                    .set(ZdPage::getUrl, pageCustomUrlDTO.getUrl())
                    .set(ZdPage::getPublishType, publishType)
                    .set(ZdPage::getUpdateTimeStamp, new Date())
                    .set(ZdPage::getAttachmentId, attachmentId)
                    .eq(ZdPage::getCode, pageCustomUrlDTO.getCode()));


        });
    }

    @Override
    public List<ZdOptionDTO<Long>> groupList(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCode)
                .eq(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .eq(ZdPage::getParentId, 0)
                .select(ZdPage::getId, ZdPage::getName);
        List<ZdPage> list = dao.list(order(queryWrapper));
        return list.stream().map(page -> new ZdOptionDTO<>(page.getName(), page.getId())).collect(Collectors.toList());
    }

    @Override
    public List<ZdPageDTO> groupTree(String appCode) {
        LambdaQueryWrapper<ZdPage> queryWrapper = appCodeQuery(appCode)
                .eq(ZdPage::getPageType, PageTypeEnum.GROUP_PAGE)
                .select(ZdPage::getId, ZdPage::getName, ZdPage::getParentId, ZdPage::getSort);
        List<ZdPage> list = dao.list(order(queryWrapper));
        List<ZdPageDTO> dtoList = mapstruct.toDTOList(list);
        return TreeDataMaker.tree(dtoList);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCodeVal() {
        LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getParentId, 0)
                .select(ZdPage::getId, ZdPage::getCodeVal, ZdPage::getSort, ZdPage::getCreateTimeStamp)
                .orderByAsc(ZdPage::getSort, ZdPage::getCreateTimeStamp);
        List<ZdPage> list = dao.list(queryWrapper);
        for (int i = 0; i < list.size(); i++) {
            ZdPage zdPage = list.get(i);
            String codeVal = i + 1 + ";";
            changeChildCodeVal(zdPage.getId(), zdPage.getCodeVal(), codeVal);
            if (!Objects.equals(zdPage.getCodeVal(), codeVal)) {
                LambdaUpdateWrapper<ZdPage> updateQuery = Wrappers.<ZdPage>lambdaUpdate()
                        .set(ZdPage::getCodeVal, codeVal)
                        .eq(ZdPage::getId, zdPage.getId());
                dao.update(updateQuery);
            }
        }
    }

    @Override
    public IPage<ZdPageSquareDTO> squareSearch(ZdPageSquareSearchParamDTO paramDTO) {
        IPage<ZdPageSquarePO> page = dao.getBaseMapper().squareSearch(paramDTO.buildPage(), BeanUtil.beanToMap(paramDTO));
        IPage<ZdPageSquareDTO> zdPageSquareDTOIPage = ConvertUtil.pageCopy(page, mapstruct::toSquareDTOList);
        List<ZdPageSquareDTO> records = zdPageSquareDTOIPage.getRecords();
        orgUtil.setOrgNameCustom(records, ZdPageSquareDTO::getCreatorIds, ZdPageServiceImpl::setCreators);
        return zdPageSquareDTOIPage;
    }

    public ZdPage getByCode(String code) {
        return dao.getOne(codeQuery(code));
    }

    private static void setCreators(ZdPageSquareDTO a, Map<String, String> map) {
        List<String> creatorIds = a.getCreatorIds();
        if (CollectionUtils.isNotEmpty(creatorIds)) {
            a.setCreators(creatorIds.stream().map(map::get).filter(Objects::nonNull).collect(Collectors.toList()));
        }
    }


    private LambdaQueryWrapper<ZdPage> appCodeQuery(Object appCode) {
        return eqOrIn(ZdPage::getAppCode, appCode);
    }

    private LambdaQueryWrapper<ZdPage> codeQuery(Object code) {
        return eqOrIn(ZdPage::getCode, code);
    }


    private void changeChildCodeVal(Long parentId, String oldCodeVal, String newCodeVal) {
        if (!Objects.equals(oldCodeVal, newCodeVal)) {
            LambdaQueryWrapper<ZdPage> queryWrapper = eqOrIn(ZdPage::getParentId, parentId)
                    .select(ZdPage::getId, ZdPage::getCodeVal);
            List<ZdPage> list = dao.list(queryWrapper);
            list.forEach(zdPage -> {
                // 需要替换第一个
                String codeVal1 = zdPage.getCodeVal();
                String codeVal2 = codeVal1.replaceFirst(oldCodeVal, newCodeVal);
                LambdaUpdateWrapper<ZdPage> updateQuery = Wrappers.<ZdPage>lambdaUpdate()
                        .set(ZdPage::getCodeVal, codeVal2)
                        .eq(ZdPage::getId, zdPage.getId());
                dao.update(updateQuery);
                changeChildCodeVal(zdPage.getId(), codeVal1, codeVal2);
            });
        }
    }


    private static LambdaQueryWrapper<ZdPage> order(LambdaQueryWrapper<ZdPage> queryWrapper) {
        queryWrapper.orderByAsc(ZdPage::getSort)
                .orderByDesc(ZdPage::getCreateTimeStamp);
        return queryWrapper;
    }

    private void check(List<ZdPagePublishDTO> pageCustomUrlList) {
        pageCustomUrlList.forEach(pageCustomUrlDTO -> {
            String url = pageCustomUrlDTO.getUrl();
            LamWrapper<ZdPage> queryWrapper = LamWrapper.eqOrIn(ZdPage::getUrl, url)
                    .select(ZdPage::getCode);
            String newCode = stringValue(queryWrapper);
            if (StringUtils.isNotBlank(newCode) && !Objects.equals(pageCustomUrlDTO.getCode(), newCode)) {
                throw new BussinessException("3000018", url);
            }
        });
    }

    /**
     * 根据页面编码获取对应的主键ID
     *
     * @param code 页面编码
     * @return 返回对应的主键ID，如果不存在则可能返回null
     */
    private Long getIdByCode(String code) {
        LamWrapper<ZdPage> queryWrapper = LamWrapper.eqOrIn(ZdPage::getCode, code)
                .select(ZdPage::getId);
        return longValue(queryWrapper);
    }


}




