package com.sinitek.sirm.nocode.page.controller;

import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.form.dto.ZdDataRightDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormCodeDTO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormDataRightService;
import com.sinitek.sirm.nocode.page.dto.ZdPageFavoritesDTO;
import com.sinitek.sirm.nocode.page.enumerate.OperationAuthEnum;
import com.sinitek.sirm.nocode.page.service.IZdPageFavoritesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 页面收藏控制器
 *
 * <AUTHOR>
 * @version 2025.0814
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Api(tags = "页面收藏管理", value = "/frontend/api/nocode/page/favorites")
@RestController
@RequestMapping("frontend/api/nocode/page/favorites")
@RequiredArgsConstructor
public class ZdPageFavoritesController {

    private final IZdPageFavoritesService pageFavoritesService;

    private final IZdPageFormDataRightService zdPageFormDataRightService;

    @ApiOperation(value = "收藏页面")
    @PostMapping("/save")
    public RequestResult<Boolean> saveOrUpdate(
            @ApiParam(value = "页面编码")
            @Valid
            @RequestBody
            ZdFormCodeDTO formCodeDTO) {
        ZdDataRightDTO zdDataRightDTO = new ZdDataRightDTO();
        zdDataRightDTO.setFormCode(formCodeDTO.getFormCode());
        // 看看有没有提交权限
        zdDataRightDTO.setOperationAuthType(OperationAuthEnum.SUBMIT);
        zdPageFormDataRightService.checkRight(zdDataRightDTO);
        ZdPageFavoritesDTO param = new ZdPageFavoritesDTO();
        param.setFormCode(formCodeDTO.getFormCode());
        param.setOrgId(CurrentUserFactory.getOrgId());
        return new RequestResult<>(pageFavoritesService.saveOrUpdate(param));
    }

    @ApiOperation(value = "取消收藏页面")
    @PostMapping("/cancel")
    public RequestResult<Boolean> cancel(
            @ApiParam(value = "页面编码")
            @Valid
            @RequestBody
            ZdFormCodeDTO formCodeDTO) {
        return new RequestResult<>(pageFavoritesService.delete(formCodeDTO.getFormCode(), CurrentUserFactory.getOrgId()));
    }
}
