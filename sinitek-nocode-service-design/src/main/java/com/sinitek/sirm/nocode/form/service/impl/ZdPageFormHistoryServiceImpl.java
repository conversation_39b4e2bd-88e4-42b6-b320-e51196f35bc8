package com.sinitek.sirm.nocode.form.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinitek.sirm.nocode.app.constant.AppConstant;
import com.sinitek.sirm.nocode.app.event.AppDeleteEvent;
import com.sinitek.sirm.nocode.common.utils.ZdOrgUtil;
import com.sinitek.sirm.nocode.form.dao.ZdPageFormHistoryDAO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormHistoryDTO;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormHistory;
import com.sinitek.sirm.nocode.form.mapper.ZdPageFormHistoryMapper;
import com.sinitek.sirm.nocode.form.mapstruct.ZdPageFormHistoryMapstruct;
import com.sinitek.sirm.nocode.form.po.ZdPageFormHistoryPO;
import com.sinitek.sirm.nocode.form.service.IZdPageFormHistoryService;
import com.sinitek.sirm.nocode.support.BaseDAO;
import com.sinitek.sirm.nocode.support.mybatis.conditions.query.LamWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025-03-13 16:13:23
 * @description 针对表【zd_page_form_history(页面表单历史表)】的数据库操作Service实现
 */
@Service
@Slf4j
public class ZdPageFormHistoryServiceImpl extends BaseDAO<ZdPageFormHistoryMapper, ZdPageFormHistory, ZdPageFormHistoryDAO>
        implements IZdPageFormHistoryService {
    @Resource
    private ZdOrgUtil zdOrgUtil;
    @Resource
    private ZdPageFormHistoryMapstruct mapstruct;


    @EventListener(condition = "#appDeleteEvent.next.equals('" + AppConstant.DE_FORM_CONFIG + "')")
    @Transactional(rollbackFor = Exception.class)
    public void delete(AppDeleteEvent appDeleteEvent) {
        List<Long> formIdList = appDeleteEvent.getIdList();
        LambdaQueryWrapper<ZdPageFormHistory> queryWrapper = eqOrIn(ZdPageFormHistory::getPageFormId, formIdList);
        dao.remove(queryWrapper);
        log.info("删除表单历史成功！");
    }

    @Override
    public Long create(ZdPageFormHistoryDTO pageFormHistoryDTO) {
        ZdPageFormHistory zdPageFormHistory = mapstruct.toEntity(pageFormHistoryDTO);
        // 创建历史记录
        dao.save(zdPageFormHistory);
        return zdPageFormHistory.getId();
    }

    @Override
    public List<ZdPageFormHistoryDTO> findByFormId(Long formId) {
        return findByFormIdList(Collections.singletonList(formId));
    }

    @Override
    public List<ZdPageFormHistoryDTO> findByFormIdList(List<Long> formIdList) {
        List<ZdPageFormHistoryPO> formHistoryList = getBaseMapper().findByFormIdList(formIdList);
        List<ZdPageFormHistoryDTO> dtoList = mapstruct.poToDTOList(formHistoryList);
        zdOrgUtil.setOrgName(dtoList, ZdPageFormHistoryDTO::getCreatorId, ZdPageFormHistoryDTO::setCreator);
        return dtoList;

    }

    @Override
    public ZdPageFormHistoryDTO getById(Long id) {
        return mapstruct.toDTO(dao.getById(id));
    }

    @Override
    public Long countByFormId(List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return 1L;
        }
        LamWrapper<ZdPageFormHistory> query = LamWrapper.eqOrIn(ZdPageFormHistory::getPageFormId, formIdList)
                .max(ZdPageFormHistory::getUpdateVersion);
        Long max = longValue(query);
        if (Objects.isNull(max)) {
            return 1L;
        }
        return max + 1;
    }
}




