package com.sinitek.sirm.nocode.page.support.repeat.base;

import com.sinitek.sirm.nocode.common.constant.CacheKeyConstant;
import com.sinitek.sirm.nocode.common.utils.ZdDateUtil;
import com.sinitek.sirm.routine.holiday.service.IHolidaysService;
import lombok.AllArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * 节假日缓存服务
 *
 * <AUTHOR>
 * @version 2025.0813
 * @since 1.0.0-SNAPSHOT
 */
@Component
@AllArgsConstructor
public class HolidaysCacheService {
    private final IHolidaysService holidaysService;


    /**
     * 检查指定日期是否为节假日
     *
     * @param date 需要检查的日期
     * @return true 表示是节假日，false 表示不是节假日
     */
    @Cacheable(value = CacheKeyConstant.HOLIDAYS, key = "T(com.sinitek.sirm.nocode.common.utils.ZdDateUtil).format(#date, 'yyyyMMdd')")
    public boolean checkHolidays(LocalDate date) {
        return holidaysService.checkHolidays(ZdDateUtil.toDate(date));
    }

}
