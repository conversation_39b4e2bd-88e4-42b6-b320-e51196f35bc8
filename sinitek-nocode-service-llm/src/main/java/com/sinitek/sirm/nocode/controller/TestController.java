package com.sinitek.sirm.nocode.controller;

/**
 * <AUTHOR>
 * @version 2025.0709
 * @since 1.0.0-SNAPSHOT
 */

public class TestController {

     /* if (Objects.isNull(setting)) {
            AiModelConfig.ModelAppInfo modelAppInfo = aiModelConfig.findByAppKey("10828dbb-37f3-4ed8-a6ea-e2e31f6d7308");
            AiModelConfig.Model model = modelAppInfo.getModel();
            String key = model.getKey();
            WebClient webClient = webClientMap.get(key);
            AiModelConfig.ModelApp modelApp = modelAppInfo.getModelApp();
            String apiKey = modelApp.getApiKey();
            Map<String, Object> bodyValue = new HashMap<>();
            bodyValue.put("response_mode", "streaming");
            bodyValue.put("user", "sinitek");
            bodyValue.put("query", "spring  WebClient  使用详解");
            bodyValue.put("inputs", new HashMap<>());
            Mono<RequestResult<String>> authorization = webClient.post()
                    .uri("v1/chat-messages")
                    .contentType(MediaType.APPLICATION_JSON)
                    .header("Authorization", "Bearer " + apiKey)
                    .bodyValue(bodyValue)
                    .accept(MediaType.TEXT_EVENT_STREAM) // 关键：接收流式响应
                    //.accept(MediaType.APPLICATION_JSON) // 关键：接收流式响应
                    .retrieve()
                    .bodyToFlux(LImChatReplyResponseDTO.class)
                    .mapNotNull(a -> a)
                    .doOnComplete(() -> {
                        log.info("结束");
                    })
                    .collectList()
                    .map(rList -> {
                        List<String> collect = rList.stream().map(LImChatReplyResponseDTO::getAnswer)
                                .collect(Collectors.toList());
                        RequestResult<String> r = new RequestResult<>();
                        r.setData(String.join("", collect));
                        return r;
                    })
                    .timeout(Duration.ofSeconds(1800))
                    .subscribeOn(Schedulers.boundedElastic());

            return authorization;


        }*/


    //header(modelAppInfo, paramMap);

      /*  return response
                //.bodyToFlux(new ServerSentEventParameterizedTypeReference())
                .bodyToFlux(LImChatReplyResponseDTO.class)
                //.bodyToFlux(BodyExtractors.toFlux(String.class))

                .doOnComplete(() -> {
                    log.info("结束");
                }).collectList()
                .map(rList -> {
                    StringBuilder fullResponse = new StringBuilder();
                    *//*for (LImChatReplyResponseDTO r : rList) {
                        //streamingResponseHandler.processLine(string);
                        fullResponse.append(r.getAnswer());
                    }*//*
                    RequestResult<String> r = new RequestResult<>();
                    r.setData(fullResponse.toString().replaceAll("<think>.*?</think>", "").replaceAll("\n+", System.lineSeparator()));
                    return r;


                });*/


    /*return webClient.post()
                .uri(modelAppInfo.findUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + apiKey)
                .bodyValue(bodyValue)
                .accept(MediaType.TEXT_EVENT_STREAM) // 关键：接收流式响应
                .retrieve();
                .bodyToFlux(String.class)
                .onBackpressureBuffer(100) // 缓冲100个元素
                .delayElements(Duration.ofMillis(50)) // 控制处理速率
                .filter(Objects::nonNull); // 过滤结束信号*/
}
