package com.sinitek.sirm.nocode.support.ask;

import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.common.dto.ZdKeyAndValueDTO;
import com.sinitek.sirm.nocode.common.enumerate.YesOrNoEnum;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.form.enumerate.AiMarkdownConversionTypeEnum;
import com.sinitek.sirm.nocode.form.enumerate.AiOutFormatEnum;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.support.ask.base.ParamAndResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.CorePublisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2025.0708
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class DifyParamAndResponseHandler implements ParamAndResponseHandler {
    @Override
    public Map<String, Object> handleParam(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo) {
        Map<String, Object> paramMap = new HashMap<>();
        String inputContent = settingDTO.getInputContent();
        // 先固定为流式
        paramMap.put("response_mode", "streaming");
        // 用户
        paramMap.put("user", "sinitek");
        HashMap<Object, Object> inputs = new HashMap<>();
        List<ZdKeyAndValueDTO> inputVar = settingDTO.getInputVar();
        if (CollectionUtils.isNotEmpty(inputVar)) {
            inputVar.forEach(zdKeyAndValueDTO -> inputs.put(zdKeyAndValueDTO.getKey(), zdKeyAndValueDTO.getValue()));
        }
        // 变量
        paramMap.put("inputs", inputs);
        YesOrNoEnum markdownConversion = settingDTO.getMarkdownConversion();
        // 输出格式
        AiOutFormatEnum outFormat = settingDTO.getOutFormat();
        boolean markdownConversionFlag = markdownConversion == YesOrNoEnum.YES;
        if (Objects.equals(outFormat, AiOutFormatEnum.MARKDOWN) || markdownConversionFlag) {
            inputContent += "\n\n请用markdown格式回答上面问题。";
            if (markdownConversionFlag) {
                List<Integer> markdownConversionType = settingDTO.getMarkdownConversionType();
                if (CollectionUtils.isEmpty(markdownConversionType)) {
                    String rules = "\n\n补充规则:\n" + markdownConversionType.stream().map(AiMarkdownConversionTypeEnum::fromValue).filter(Objects::nonNull).map(AiMarkdownConversionTypeEnum::getRule).collect(Collectors.joining(System.lineSeparator()));
                    inputContent += rules;
                }
            }
        } else if (Objects.equals(outFormat, AiOutFormatEnum.TEXT)) {
            inputContent += "\n\n请用纯文本格式回答上面问题。";
        } else if (Objects.equals(outFormat, AiOutFormatEnum.PIC)) {
            inputContent += "\n\n请用图片格式输出回答内容。";
        }

        // 聊天内容
        paramMap.put("query", inputContent);
        return paramMap;


    }

    @SuppressWarnings("unchecked")
    @Override
    public <R, T extends CorePublisher<R>> T handleResponse(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo, WebClient.RequestHeadersSpec<?> headersSpec, Class<T> clazz) {
        StopWatch stopWatch = new StopWatch();
        AiModelConfig.Model model = modelAppInfo.getModel();
        String server = model.getServer();
        log.info("请求开始：{}", server + modelAppInfo.findUrl());
        stopWatch.start("大模型调用:" + model.getName());
        Flux<String> flux = headersSpec.
                retrieve()
                .bodyToFlux(LImChatReplyResponseDTO.class)
                .mapNotNull(LImChatReplyResponseDTO::getAnswer)
                .doOnComplete(() -> watch(stopWatch));
        if (Objects.equals(clazz, Mono.class)) {
            return (T) flux.collectList()
                    .map(rList -> {
                        RequestResult<String> responseResult = new RequestResult<>();
                        String result = String.join("", rList);
                        result = result.replaceAll("(?s)<think>.*?</think>", "");
                        responseResult.setData(result);
                        return responseResult;
                    })
                    .subscribeOn(Schedulers.boundedElastic());
        } else {
            return (T) flux;
        }
    }

    /**
     * sk-7f168e1cbf884b73be3981c5da293242
     * 观察 耗时情况
     *
     * @param stopWatch stopWatch
     */
    private static void watch(StopWatch stopWatch) {
        stopWatch.stop();
        log.info("{}{}耗时{}毫秒", stopWatch.prettyPrint(), System.lineSeparator(), stopWatch.getTotalTimeMillis());
    }


    @Override
    public boolean support(AiModelConfig.ModelAppInfo modelAppInfo) {
        AiModelConfig.Model model = modelAppInfo.getModel();
        return "dify".equals(model.getKey());
    }
}
