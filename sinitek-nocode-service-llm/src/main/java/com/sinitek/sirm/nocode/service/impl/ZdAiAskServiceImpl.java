package com.sinitek.sirm.nocode.service.impl;

import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.form.enumerate.AiOutFormatEnum;
import com.sinitek.sirm.nocode.form.service.IZdPageFormAiQuestionSettingService;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.llm.service.IZdAiAskService;
import com.sinitek.sirm.nocode.support.ask.DifyParamAndResponseHandler;
import com.sinitek.sirm.nocode.support.ask.base.ParamAndResponseHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0703
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZdAiAskServiceImpl implements IZdAiAskService {
    private final IZdPageFormAiQuestionSettingService settingService;
    private final AiModelConfig aiModelConfig;
    private final Map<String, WebClient> webClientMap;
    private final DifyParamAndResponseHandler paramAndResponseHandler;

    private final ObjectProvider<List<ParamAndResponseHandler>> paramAndResponseHandlers;


    @SuppressWarnings("unchecked")
    @Override
    public Mono<RequestResult<String>> ask(ZdPageFormAiQuestionDTO request) {
        ZdPageFormAiQuestionSettingDTO setting = settingService.getById(request.getId());

        if (Objects.isNull(setting)) {
            setting = new ZdPageFormAiQuestionSettingDTO();
            setting.setModelCode("07dca092-3f98-4114-8f26-3bb62d79b053");
            setting.setModelCode("deepseek-chat-v1");
            setting.setInputContent("spring  WebClient 使用详解");
            setting.setOutFormat(AiOutFormatEnum.TEXT);
        }

        String modelCode = setting.getModelCode();
        AiModelConfig.ModelAppInfo modelAppInfo = validateAndGetModelAppInfo(modelCode);
        ParamAndResponseHandler handler = getHandler(modelAppInfo);
        Map<String, Object> paramMap = handler.handleParam(setting, modelAppInfo);
        return handler.handleResponse(setting, modelAppInfo, header(modelAppInfo, paramMap), Mono.class);
    }

    /**
     * 获取适配指定模型配置的参数与响应处理器
     *
     * @param modelAppInfo 模型应用配置信息，用于确定处理器适配性
     * @return 适配的参数与响应处理器实例
     */
    private ParamAndResponseHandler getHandler(AiModelConfig.ModelAppInfo modelAppInfo) {
        // 初始化默认处理器
        ParamAndResponseHandler handler = paramAndResponseHandler;

        // 从缓存获取可用处理器列表（避免重复创建）
        List<ParamAndResponseHandler> handlers = paramAndResponseHandlers.getIfAvailable();

        // 优先使用缓存中的适配处理器
        if (CollectionUtils.isNotEmpty(handlers)) {
            for (ParamAndResponseHandler paramAndResponseHandler : handlers) {
                // 发现首个适配当前模型配置的处理器时立即采用
                if (paramAndResponseHandler.support(modelAppInfo)) {
                    handler = paramAndResponseHandler;
                    break;
                }
            }
        }
        return handler;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Flux<String> askStream(ZdPageFormAiQuestionDTO request) {
        ZdPageFormAiQuestionSettingDTO setting = settingService.getById(request.getId());
        String modelCode = setting.getModelCode();
        AiModelConfig.ModelAppInfo modelAppInfo = validateAndGetModelAppInfo(modelCode);
        ParamAndResponseHandler handler = getHandler(modelAppInfo);
        Map<String, Object> paramMap = handler.handleParam(setting, modelAppInfo);
        return handler.handleResponse(setting, modelAppInfo, header(modelAppInfo, paramMap), Flux.class);
    }

    /**
     * 验证模型有效性并获取模型应用信息
     *
     * @param modelCode 模型编码
     * @return 模型应用信息
     * @throws BussinessException 当模型不存在时抛出异常
     */
    private AiModelConfig.ModelAppInfo validateAndGetModelAppInfo(String modelCode) {
        AiModelConfig.ModelAppInfo modelAppInfo = aiModelConfig.findByAppKey(modelCode);
        if (Objects.isNull(modelAppInfo)) {
            log.error("未找到模型应用");
            throw new BussinessException("3000021", modelCode);
        }
        return modelAppInfo;
    }

    /**
     * 构建WebClient请求头配置，设置认证信息和内容类型。
     *
     * @param modelAppInfo 包含模型应用配置的对象，用于获取模型密钥和API密钥
     * @param bodyValue    请求体数据，将作为JSON格式发送
     * @return WebClient.RequestHeadersSpec<?> 配置好的请求头对象
     */
    private WebClient.RequestHeadersSpec<?> header(AiModelConfig.ModelAppInfo modelAppInfo, Map<String, Object> bodyValue) {
        AiModelConfig.Model model = modelAppInfo.getModel();
        String key = model.getKey();
        WebClient webClient = webClientMap.get(key);
        AiModelConfig.ModelApp modelApp = modelAppInfo.getModelApp();
        String apiKey = modelApp.getApiKey();


        return webClient.post()
                .uri(modelAppInfo.findUrl())
                .contentType(MediaType.APPLICATION_JSON)
                .header("Authorization", "Bearer " + apiKey)
                .bodyValue(bodyValue)
                .accept(MediaType.TEXT_EVENT_STREAM);
    }
}
