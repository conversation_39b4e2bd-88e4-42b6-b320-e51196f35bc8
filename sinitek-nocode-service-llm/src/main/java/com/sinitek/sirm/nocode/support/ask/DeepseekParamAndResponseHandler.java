package com.sinitek.sirm.nocode.support.ask;

import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.nocode.dto.DeepSeek2ChatCompletionResponseDTO;
import com.sinitek.sirm.nocode.dto.LImChatReplyResponseBaseDTO;
import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import com.sinitek.sirm.nocode.support.ask.base.ParamAndResponseHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.CorePublisher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0709
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Component
public class DeepseekParamAndResponseHandler implements ParamAndResponseHandler {
    @Override
    public Map<String, Object> handleParam(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo) {
        AiModelConfig.ModelType modelType = modelAppInfo.getModelType();
        String key = modelType.getKey();

        Map<String, Object> paramMap = new HashMap<>();
        // 模型
        paramMap.put("model", key);
        // 提示词
        String prompt = ParamAndResponseHandler.prompt(settingDTO);
        List<Map<String, Object>> messages = new ArrayList<>();
        Map<String, Object> oneMessage = new HashMap<>();
        oneMessage.put("role", "user");
        oneMessage.put("content", prompt);
        messages.add(oneMessage);
        paramMap.put("messages", messages);
        paramMap.put("stream", true);
        Map<String, Object> streamOptions = new HashMap<>();
        streamOptions.put("include_usage", true);
        paramMap.put("stream_options", streamOptions);
        return paramMap;
    }

    @Override
    public <R, T extends CorePublisher<R>> T handleResponse(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.Model model, WebClient.RequestHeadersSpec<?> headersSpec, Class<T> clazz) {
        return handleResponse(settingDTO, model, headersSpec, clazz, str -> {
            DeepSeek2ChatCompletionResponseDTO a = JsonUtil.toJavaObject(str, DeepSeek2ChatCompletionResponseDTO.class);
            List<DeepSeek2ChatCompletionResponseDTO.ChoiceDTO> choices = a.getChoices();
            if (CollectionUtils.isNotEmpty(choices)) {
                return choices.stream().map(DeepSeek2ChatCompletionResponseDTO.ChoiceDTO::getDelta)
                        .filter(Objects::nonNull)
                        .map(DeepSeek2ChatCompletionResponseDTO.Delta::getContent)
                        .filter(Objects::nonNull)
                        .map(b -> {
                            LImChatReplyResponseBaseDTO baseDTO = new LImChatReplyResponseBaseDTO();
                            baseDTO.setAnswer(b);
                            return baseDTO;
                        }).findFirst().orElse(null);
            }
            return new LImChatReplyResponseBaseDTO();
        });
    }

    @Override
    public boolean support(AiModelConfig.ModelAppInfo modelAppInfo) {
        AiModelConfig.Model model = modelAppInfo.getModel();
        return Objects.equals(model.getKey(), "deepseek");
    }
}
