package com.sinitek.sirm.nocode.support.ask.base;

import com.sinitek.sirm.nocode.form.dto.ZdPageFormAiQuestionSettingDTO;
import com.sinitek.sirm.nocode.llm.config.AiModelConfig;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.CorePublisher;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 2025.0708
 * @since 1.0.0-SNAPSHOT
 */

public interface ParamAndResponseHandler {
    Map<String, Object> handleParam(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo);

    <R, T extends CorePublisher<R>> T handleResponse(ZdPageFormAiQuestionSettingDTO settingDTO, AiModelConfig.ModelAppInfo modelAppInfo, WebClient.RequestHeadersSpec<?> headersSpec, Class<T> clazz);

    /**
     * 是否支持该模型应用
     *
     * @param modelAppInfo 模型应用信息
     * @return true:支持;false:不支持
     */
    boolean support(AiModelConfig.ModelAppInfo modelAppInfo);


}
