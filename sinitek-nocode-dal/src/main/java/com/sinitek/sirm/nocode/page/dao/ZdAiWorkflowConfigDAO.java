package com.sinitek.sirm.nocode.page.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinitek.sirm.nocode.page.entity.ZdAiWorkflowConfig;
import com.sinitek.sirm.nocode.page.mapper.ZdAiWorkflowConfigMapper;
import com.sinitek.sirm.nocode.page.po.ZdAiWorkflowBindParamPO;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 2025.0407
 * @description
 * @since 1.0.0-SNAPSHOT
 */
@Slf4j
@Repository
public class ZdAiWorkflowConfigDAO extends
    ServiceImpl<ZdAiWorkflowConfigMapper, ZdAiWorkflowConfig> {

    public Long bind(ZdAiWorkflowBindParamPO param) {
        String appId = param.getAppId();
        String formCode = param.getFormCode();
        String eventType = param.getEventType();
        String operatorId = param.getOperatorId();
        Date operateTime = param.getOperateTime();

        List<ZdAiWorkflowConfig> list = this.findByAppId(appId);
        if (CollUtil.isNotEmpty(list)) {
            // 之前绑定过,选择第一个重新绑定
            int size = list.size();
            if (size > 1) {
                log.warn("工作流 {} 存在多条 {} ai工作流绑定,只取第一条数据", appId, size);
            }
            ZdAiWorkflowConfig zdAiWorkflowConfig = list.get(0);
            zdAiWorkflowConfig.setFormCode(formCode);
            zdAiWorkflowConfig.setEventType(eventType);
            zdAiWorkflowConfig.setAppId(appId);
            zdAiWorkflowConfig.setApiKey(null);
            zdAiWorkflowConfig.setOperatorId(operatorId);
            zdAiWorkflowConfig.setOperateTime(operateTime);
            this.updateById(zdAiWorkflowConfig);
            return zdAiWorkflowConfig.getId();
        } else {
            // 之前没有绑定过,重新绑定
            ZdAiWorkflowConfig zdAiWorkflowConfig = new ZdAiWorkflowConfig();
            zdAiWorkflowConfig.setFormCode(formCode);
            zdAiWorkflowConfig.setEventType(eventType);
            zdAiWorkflowConfig.setAppId(appId);
            zdAiWorkflowConfig.setApiKey(null);
            zdAiWorkflowConfig.setOperatorId(operatorId);
            zdAiWorkflowConfig.setOperateTime(operateTime);
            this.save(zdAiWorkflowConfig);
            return zdAiWorkflowConfig.getId();
        }
    }

    public void updateApiKey(Long id, String encryptKey) {
        ZdAiWorkflowConfig data = this.getById(id);
        if (Objects.nonNull(data)) {
            data.setApiKey(encryptKey);
            this.updateById(data);
        } else {
            log.warn("根据 {} 没有找到对应的ai工作流绑定数据", id);
        }
    }

    public List<ZdAiWorkflowConfig> findByAppId(String appId) {
        LambdaQueryWrapper<ZdAiWorkflowConfig> queryWrapper = Wrappers.lambdaQuery(
            ZdAiWorkflowConfig.class);
        queryWrapper.eq(ZdAiWorkflowConfig::getAppId, appId);
        return this.list(queryWrapper);
    }

    public List<ZdAiWorkflowConfig> find(String formCode) {
        LambdaQueryWrapper<ZdAiWorkflowConfig> queryWrapper = Wrappers.lambdaQuery(
            ZdAiWorkflowConfig.class);
        queryWrapper.eq(ZdAiWorkflowConfig::getFormCode, formCode);
        return this.list(queryWrapper);
    }

    public List<ZdAiWorkflowConfig> find(String formCode, String eventType) {
        LambdaQueryWrapper<ZdAiWorkflowConfig> queryWrapper = Wrappers.lambdaQuery(
            ZdAiWorkflowConfig.class);
        queryWrapper.eq(ZdAiWorkflowConfig::getFormCode, formCode);
        queryWrapper.eq(ZdAiWorkflowConfig::getEventType, eventType);
        return this.list(queryWrapper);
    }

    public boolean deleteByFormCodes(Collection<String> formCodes) {
        LambdaQueryWrapper<ZdAiWorkflowConfig> queryWrapper = Wrappers.lambdaQuery(
            ZdAiWorkflowConfig.class);
        queryWrapper.in(ZdAiWorkflowConfig::getFormCode, formCodes);
        return this.remove(queryWrapper);
    }

    public boolean deleteByAppId(String appId) {
        LambdaQueryWrapper<ZdAiWorkflowConfig> queryWrapper = Wrappers.lambdaQuery(
            ZdAiWorkflowConfig.class);
        queryWrapper.in(ZdAiWorkflowConfig::getAppId, appId);
        return this.remove(queryWrapper);
    }

}
