-- 页面收藏表
create table if not exists zd_app_ai_setting
(
    id              bigint      not null
        primary key,
    app_code        varchar(50) not null,
    model_name      varchar(50) not null,
    model_key       varchar(50) not null,
    model_type_name varchar(50) not null,
    model_type_key  varchar(50) not null,
    model_app_name  varchar(50) not null,
    model_app_key   varchar(50) not null

);

-- 应用ai设置表
comment on table zd_app_ai_setting is '页面收藏表';
comment on column zd_app_ai_setting.id is '主键';
comment on column zd_app_ai_setting.model_key is 'ai源的key';
comment on column zd_app_ai_setting.model_type_name is '模型类型名称';
comment on column zd_app_ai_setting.model_type_key is '模型类型key';
comment on column zd_app_ai_setting.model_app_name is '模型应用名称';
comment on column zd_app_ai_setting.model_app_key is '模型应用key';


-- 索引
create index if not exists idx_zd_app_ai_setting_code on zd_app_ai_setting (app_code);