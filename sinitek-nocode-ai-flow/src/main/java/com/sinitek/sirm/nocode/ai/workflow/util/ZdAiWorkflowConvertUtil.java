package com.sinitek.sirm.nocode.ai.workflow.util;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowApiKeyProcessType;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowBindParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigInnerDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdFormAiWorkflowInfoDTO;
import com.sinitek.sirm.nocode.page.entity.ZdAiWorkflowConfig;
import com.sinitek.sirm.nocode.page.po.ZdAiWorkflowBindParamPO;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

/**
 * <AUTHOR>
 * @date 2025-08-12 17:03
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ZdAiWorkflowConvertUtil {

    private static final int REF_INDEX = 1;

    private static final int CHILD_REF_INDEX = 2;

    public static Tuple3<String, String, String> getEventWrapper(String eventType) {
        List<String> splitList = Arrays.stream(eventType.split("@")).collect(Collectors.toList());
        String type = "";
        String ref = "";
        String childRef = "";

        if (CollUtil.isNotEmpty(splitList)) {
            type = splitList.get(0);
            int size = splitList.size();
            if (size > REF_INDEX) {
                ref = splitList.get(REF_INDEX);
            }
            if (size >= CHILD_REF_INDEX) {
                childRef = splitList.get(CHILD_REF_INDEX);
            }
        }

        return Tuples.of(type, ref, childRef);
    }

    public static ZdAiWorkflowConfigDTO toDTO(ZdAiWorkflowConfig entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        ZdAiWorkflowConfigDTO result = new ZdAiWorkflowConfigDTO();

        String eventType = entity.getEventType();
        Tuple3<String, String, String> eventWrapper = getEventWrapper(eventType);

        result.setType(eventWrapper.getT1());
        result.setRefs(CollUtil.toList(eventWrapper.getT2(), eventWrapper.getT3()));

        result.setAppId(entity.getAppId());
        result.setOperatorId(entity.getOperatorId());
        result.setOperateTime(entity.getOperateTime());
        result.setFormCode(entity.getFormCode());
        result.setId(entity.getId());

        String apiKey = entity.getApiKey();
        if (StringUtils.isNotBlank(apiKey)) {
            result.setApiKeyProcessType(ZdAiWorkflowApiKeyProcessType.USE_EXISTING);
        } else {
            result.setApiKeyProcessType(ZdAiWorkflowApiKeyProcessType.MANUAL_INPUT);
        }

        return result;
    }

    public static ZdFormAiWorkflowInfoDTO toFormAiWorkflowDTO(ZdAiWorkflowConfig entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        ZdFormAiWorkflowInfoDTO result = new ZdFormAiWorkflowInfoDTO();

        String eventType = entity.getEventType();
        Tuple3<String, String, String> eventWrapper = getEventWrapper(eventType);

        result.setType(eventWrapper.getT1());
        result.setRef(eventWrapper.getT2());
        result.setChildRef(eventWrapper.getT3());
        
        result.setAppId(entity.getAppId());
        result.setFormCode(entity.getFormCode());
        result.setBindId(entity.getId());

        return result;
    }

    public static ZdAiWorkflowConfigInnerDTO toInnerDTO(ZdAiWorkflowConfig entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        ZdAiWorkflowConfigInnerDTO result = new ZdAiWorkflowConfigInnerDTO();

        result.setEventType(entity.getEventType());
        result.setAppId(entity.getAppId());
        result.setFormCode(entity.getFormCode());
        result.setEncryptApikey(entity.getApiKey());
        result.setId(entity.getId());

        return result;
    }

    public static ZdAiWorkflowBindParamPO toPO(ZdAiWorkflowBindParamDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        ZdAiWorkflowBindParamPO result = new ZdAiWorkflowBindParamPO();

        List<String> values = new LinkedList<>();
        String type = dto.getType();
        values.add(type);
        List<String> refs = dto.getRefs();
        if (CollUtil.isNotEmpty(refs)) {
            values.addAll(refs);
        }
        String eventType = String.join("@", values);

        result.setEventType(eventType);
        result.setAppId(dto.getAppId());
        result.setOperatorId(dto.getOperatorId());
        result.setFormCode(dto.getFormCode());
        result.setOperateTime(dto.getOperateTime());

        return result;
    }

}
