package com.sinitek.sirm.nocode.ai.workflow.service;

import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowBindParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigInnerDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdFormAiWorkflowInfoDTO;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-12 17:00
 */
public interface IZdAiWorkflowService {

    ZdAiWorkflowConfigDTO getBindInfoByAppId(String appId);

    List<ZdFormAiWorkflowInfoDTO> findConfigByFormCode(String formCode);

    ZdAiWorkflowConfigInnerDTO getBindInfo(Long id);

    void bindAiWorkFlow(ZdAiWorkflowBindParamDTO param);

    void deleteByFormCodes(Collection<String> formCodes);

    void deleteByAppId(String appId);
}
