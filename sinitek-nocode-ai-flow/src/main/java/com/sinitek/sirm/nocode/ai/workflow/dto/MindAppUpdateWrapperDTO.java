package com.sinitek.sirm.nocode.ai.workflow.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-11 17:26
 */
@Data
@ApiModel("工作流更新参数")
public class MindAppUpdateWrapperDTO implements FormCodeSupplier {

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotBlank(message = "Id不能为空")
    @ApiModelProperty("应用id")
    private String appId;

    @NotBlank(message = "名称不能为空")
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("介绍")
    private String intro;

}
