package com.sinitek.sirm.nocode.ai.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:55
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ZdFormAiWorkflowInfoDTO", description = "表单AI工作流配置")
public class ZdFormAiWorkflowInfoDTO {

    @ApiModelProperty("工作流绑定id")
    private Long bindId;

    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("组件ref")
    private String ref;

    @ApiModelProperty("子表单内组件ref")
    private String childRef;

    @ApiModelProperty("事件")
    private String type;

    @ApiModelProperty("工作流应用id")
    private String appId;

}
