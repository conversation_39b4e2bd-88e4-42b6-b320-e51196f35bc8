package com.sinitek.sirm.nocode.ai.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-08-11 17:26
 */
@Data
@NoArgsConstructor
@ApiModel("工作流请求参数")
public class AiWorkflowReqeustMessageWrapperDTO {

    private static final String USER_ROLE = "user";

    @ApiModelProperty("是否流式输出")
    private Boolean stream = true;

    @ApiModelProperty("是否展示详情")
    private Boolean detail = true;

    @ApiModelProperty("消息列表")
    private List<MessageItem> messages;

    @ApiModelProperty("参数")
    private Map<String, Object> variables;

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @ApiModel("单个消息")
    public static class MessageItem {

        @ApiModelProperty("内容")
        private String content;

        @ApiModelProperty("角色")
        private String role;
    }

    public static MessageItem buildUserMessage(String content) {
        return MessageItem.builder().role(USER_ROLE).content(content).build();
    }
}
