package com.sinitek.sirm.nocode.ai.workflow.service.impl;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.REQUEST_FAILED;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.mind.common.support.ApiResponse;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppDetailDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppListItemDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.AppUpdateDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.DelAppByNamespaceParamDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.ListAppDTO;
import com.sinitek.sirm.nocode.ai.mind.core.app.dto.SimpleCreateAppParamDTO;
import com.sinitek.sirm.nocode.ai.mind.feign.IMindAppRemoteService;
import com.sinitek.sirm.nocode.ai.workflow.dto.ListAppWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.MindAppUpdateWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.SimpleCreateAppParamWrapperDTO;
import com.sinitek.sirm.nocode.ai.workflow.service.IMindAppManageService;
import com.sinitek.sirm.nocode.ai.workflow.service.IZdAiWorkflowService;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025-08-11 15:56
 */
@Slf4j
@Service
public class MindAppManageServiceImpl implements IMindAppManageService {

    private static final String WORK_FLOW_TYPE = "advanced";

    @Autowired
    private IMindAppRemoteService mindAppRemoteService;

    @Autowired
    private IZdAiWorkflowService zdAiWorkflowService;

    @Override
    public List<AppListItemDTO> findAppList(ListAppWrapperDTO param) {
        String formCode = param.getFormCode();
        ListAppDTO requestParam = new ListAppDTO();
        requestParam.setNamespace(formCode);
        requestParam.setType(Collections.singletonList(WORK_FLOW_TYPE));

        ApiResponse<List<AppListItemDTO>> response = this.mindAppRemoteService.getAppList(
            requestParam);

        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.debug("获取应用列表，参数: {},请求参数: {}, 返回值: {}",
                    JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(requestParam),
                    JsonUtil.toJsonString(response));
            }
            return response.getData();
        } else {
            if (log.isErrorEnabled()) {
                log.error("获取应用列表失败，参数: {},请求参数: {}, 返回值: {}",
                    JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(requestParam),
                    JsonUtil.toJsonString(response));
            }
            String message = response.getMessage();
            throw new BussinessException(REQUEST_FAILED, message);
        }
    }

    @Override
    public String simpleCreateApp(SimpleCreateAppParamWrapperDTO param) {
        String formCode = param.getFormCode();
        String name = param.getName();
        String intro = param.getIntro();

        SimpleCreateAppParamDTO requestParam = new SimpleCreateAppParamDTO();
        requestParam.setName(name);
        requestParam.setType(WORK_FLOW_TYPE);
        requestParam.setNamespace(formCode);
        requestParam.setIntro(intro);
        ApiResponse<String> response = this.mindAppRemoteService.simpleCreateApp(
            requestParam);

        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.debug("简单创建应用，参数: {},请求参数: {}, 返回值: {}",
                    JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(requestParam),
                    JsonUtil.toJsonString(response));
            }
            return response.getData();
        } else {
            if (log.isErrorEnabled()) {
                log.error("简单创建应用失败，参数: {},请求参数: {}, 返回值: {}",
                    JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(requestParam),
                    JsonUtil.toJsonString(response));
            }
            String message = response.getMessage();
            throw new BussinessException(REQUEST_FAILED, message);
        }
    }

    @Override
    public AppDetailDTO getAppDetail(String appId) {
        ApiResponse<AppDetailDTO> response = this.mindAppRemoteService.getAppDetail(appId);
        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.debug("获取应用详情，参数: {},请求参数: {}, 返回值: {}",
                    appId,
                    appId,
                    JsonUtil.toJsonString(response));
            }
            return response.getData();
        } else {
            if (log.isErrorEnabled()) {
                log.error("获取应用详情失败，参数: {},请求参数: {}, 返回值: {}",
                    appId,
                    appId,
                    JsonUtil.toJsonString(response));
            }
            String message = response.getMessage();
            throw new BussinessException(REQUEST_FAILED, message);
        }
    }

    @Override
    public void deleteApp(String appId) {
        ApiResponse<Void> response = this.mindAppRemoteService.deleteApp(appId);
        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.debug("删除应用，参数: {},请求参数: {}, 返回值: {}",
                    appId,
                    appId,
                    JsonUtil.toJsonString(response));
            }
            log.info("删除应用后,同步删除ai工作流绑定关系,应用id: {}", appId);
            this.zdAiWorkflowService.deleteByAppId(appId);
        } else {
            if (log.isErrorEnabled()) {
                log.error("删除应用失败，参数: {},请求参数: {}, 返回值: {}",
                    appId,
                    appId,
                    JsonUtil.toJsonString(response));
            }
            String message = response.getMessage();
            throw new BussinessException(REQUEST_FAILED, message);
        }
    }

    @Override
    public void deleteAppByNamespaces(List<String> namespaceList) {
        if (CollUtil.isNotEmpty(namespaceList)) {
            DelAppByNamespaceParamDTO param = new DelAppByNamespaceParamDTO();
            param.setNamespaceList(namespaceList);
            ApiResponse<Void> response = this.mindAppRemoteService.deleteAppByNamespace(param);
            if (response.isSuccess()) {
                if (log.isDebugEnabled()) {
                    log.debug("根据命名空间删除应用，参数: {},请求参数: {}, 返回值: {}",
                        JsonUtil.toJsonString(namespaceList),
                        JsonUtil.toJsonString(param),
                        JsonUtil.toJsonString(response));
                }
            } else {
                if (log.isErrorEnabled()) {
                    log.error("根据命名空间删除应用失败，参数: {},请求参数: {}, 返回值: {}",
                        JsonUtil.toJsonString(namespaceList),
                        JsonUtil.toJsonString(param),
                        JsonUtil.toJsonString(response));
                }
                String message = response.getMessage();
                throw new BussinessException(REQUEST_FAILED, message);
            }
        } else {
            log.warn("根据命名空间删除工作流失时,传入命名空间为空");
        }
    }

    @Override
    public String copyApp(String appId) {
        ApiResponse<String> response = this.mindAppRemoteService.copyApp(appId);
        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.debug("复制应用，参数: {},请求参数: {}, 返回值: {}",
                    appId,
                    appId,
                    JsonUtil.toJsonString(response));
            }
            return response.getData();
        } else {
            if (log.isErrorEnabled()) {
                log.error("复制应用失败，参数: {},请求参数: {}, 返回值: {}",
                    appId,
                    appId,
                    JsonUtil.toJsonString(response));
            }
            String message = response.getMessage();
            throw new BussinessException(REQUEST_FAILED, message);
        }
    }

    @Override
    public AppDTO updateApp(MindAppUpdateWrapperDTO param) {
        String appId = param.getAppId();
        String name = param.getName();
        String formCode = param.getFormCode();
        String intro = param.getIntro();

        AppUpdateDTO updateParam = new AppUpdateDTO();
        updateParam.setAppId(appId);
        updateParam.setName(name);
        updateParam.setNamespace(formCode);
        updateParam.setIntro(intro);
        ApiResponse<AppDTO> response = this.mindAppRemoteService.updateApp(appId, updateParam);
        if (response.isSuccess()) {
            if (log.isDebugEnabled()) {
                log.debug("更新应用，参数: {},请求参数: {}, 返回值: {}",
                    JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(updateParam),
                    JsonUtil.toJsonString(response));
            }
            return response.getData();
        } else {
            if (log.isErrorEnabled()) {
                log.error("更新应用失败，参数: {},请求参数: {}, 返回值: {}",
                    JsonUtil.toJsonString(param),
                    JsonUtil.toJsonString(updateParam),
                    JsonUtil.toJsonString(response));
            }
            String message = response.getMessage();
            throw new BussinessException(REQUEST_FAILED, message);
        }
    }
}
