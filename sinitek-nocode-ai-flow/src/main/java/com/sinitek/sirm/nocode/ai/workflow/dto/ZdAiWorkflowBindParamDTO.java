package com.sinitek.sirm.nocode.ai.workflow.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:37
 */
@Data
@NoArgsConstructor
@ApiModel("智搭ai工作流绑定参数")
public class ZdAiWorkflowBindParamDTO implements FormCodeSupplier {

    @NotBlank(message = "应用id不能为空")
    @ApiModelProperty("应用id")
    private String appId;

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotBlank(message = "事件不能为空")
    @ApiModelProperty("事件")
    private String type;

    @NotEmpty(message = "组件不能为空")
    @ApiModelProperty("表单编码")
    private List<String> refs;

    @ApiModelProperty("apiKey处理方式(1:手动输入,2:自动生成,3:使用已有key)")
    private Integer apiKeyProcessType;

    @ApiModelProperty("apiKey,仅处理方式为手动输入时起效果")
    private String apiKey;

    @ApiModelProperty("操作人")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;
}
