package com.sinitek.sirm.nocode.ai.workflow.dto;

import com.sinitek.sirm.nocode.form.support.FormCodeSupplier;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.Map;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:37
 */
@Data
@NoArgsConstructor
@ApiModel("智搭ai工作流触发参数")
public class ZdAiWorkflowTriggerParamDTO implements FormCodeSupplier {

    @ApiModelProperty("触发id")
    private String triggerId;

    @NotBlank(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

    @NotNull(message = "对应的绑定工作流id不能为空")
    @ApiModelProperty("对应的绑定工作流id")
    private Long bindId;

    @ApiModelProperty("页面schemaId")
    private String schemaId;

    @ApiModelProperty("页面schema")
    private String schema;

    @ApiModelProperty("页面数据")
    private Map<String, Object> model;

    @ApiModelProperty("查询参数")
    private Map<String, Object> queryParam;
    
    @ApiModelProperty("用户消息")
    private String userMessage;

    @ApiModelProperty("操作人")
    private String operatorId;

    @ApiModelProperty("操作时间")
    private Date operateTime;
}
