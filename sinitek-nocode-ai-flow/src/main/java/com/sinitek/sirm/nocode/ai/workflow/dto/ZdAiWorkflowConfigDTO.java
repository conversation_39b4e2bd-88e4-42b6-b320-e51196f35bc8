package com.sinitek.sirm.nocode.ai.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sinitek.sirm.common.utils.GlobalConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:55
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ZdAiWorkflowConfig", description = "AI工作流配置")
public class ZdAiWorkflowConfigDTO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("事件")
    private String type;

    @ApiModelProperty("表单编码")
    private List<String> refs;

    @ApiModelProperty("工作流应用id")
    private String appId;

    @ApiModelProperty("操作人id")
    private String operatorId;

    @ApiModelProperty("表单编码")
    private String formCode;

    @ApiModelProperty("apiKey处理方式(1:手动输入,2:自动生成,3:使用已有key)")
    private Integer apiKeyProcessType;

    @JsonFormat(timezone = GlobalConstant.TIME_FORMAT_THIRTEEN)
    @ApiModelProperty("操作时间")
    private Date operateTime;

}
