package com.sinitek.sirm.nocode.ai.workflow.properties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-08-07 11:33
 */
@Data
@Component
@ApiModel("ai工作流配置")
@ConfigurationProperties(prefix = "nocode.ai.workflow")
public class ZdAiWorkflowProperties {

    @ApiModelProperty("智搭工作流触发sse连接超时时间(秒)")
    private Integer sseTimeout = 300;

    @ApiModelProperty("ai连接超时时间（秒）")
    private Integer timeout = 300;

    @ApiModelProperty("ai最大连接数")
    private Integer maxConnections = 50;

    @ApiModelProperty("ai异步响应超时（秒）")
    private Integer asyncResponseTimeout = 90;

    @ApiModelProperty("等待获取连接的最大时间（秒）")
    private Integer pendingAcquireTimeout = 90;

    @ApiModelProperty("连接在池中空闲多久后会被回收/关闭（秒）")
    private Integer maxIdleTime = 30;

    @ApiModelProperty("应用api key名称")
    private String apiKeyName = "智搭应用";

    @ApiModelProperty("应用api key最大使用点数")
    private Long apiKeyMaxUsagePoint = -1L;
}
