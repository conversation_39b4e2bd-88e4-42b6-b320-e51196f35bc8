package com.sinitek.sirm.nocode.ai.workflow.service.impl;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.API_KEY_CANT_BLANK;
import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.API_KEY_IS_EMPTY;
import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowMessageConstant.CREATE_API_KEY_FAILED;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.nocode.ai.mind.common.support.ApiResponse;
import com.sinitek.sirm.nocode.ai.mind.feign.IMindApiRemoteService;
import com.sinitek.sirm.nocode.ai.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.sirm.nocode.ai.mind.support.openapi.dto.CreateApiKeyRequestDTO.LimitDTO;
import com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowApiKeyProcessType;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowBindParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigInnerDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdFormAiWorkflowInfoDTO;
import com.sinitek.sirm.nocode.ai.workflow.properties.ZdAiWorkflowProperties;
import com.sinitek.sirm.nocode.ai.workflow.service.IZdAiWorkflowService;
import com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowApiKeyUtil;
import com.sinitek.sirm.nocode.ai.workflow.util.ZdAiWorkflowConvertUtil;
import com.sinitek.sirm.nocode.page.dao.ZdAiWorkflowConfigDAO;
import com.sinitek.sirm.nocode.page.entity.ZdAiWorkflowConfig;
import com.sinitek.sirm.nocode.page.po.ZdAiWorkflowBindParamPO;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025-08-12 17:00
 */
@Slf4j
@Service
public class ZdAiWorkflowServiceImpl implements IZdAiWorkflowService {

    @Autowired
    private ZdAiWorkflowConfigDAO dao;

    @Autowired
    private ZdAiWorkflowProperties properties;

    @Autowired
    private IMindApiRemoteService mindApiRemoteService;

    @Override
    public ZdAiWorkflowConfigDTO getBindInfoByAppId(String appId) {
        List<ZdAiWorkflowConfig> list = this.dao.findByAppId(appId);
        if (CollUtil.isNotEmpty(list)) {
            int size = list.size();
            if (size > 1) {
                log.warn("工作流 {} 存在多条 {} ai工作流绑定,只取第一条数据", appId, size);
            }
            return ZdAiWorkflowConvertUtil.toDTO(list.get(0));
        }
        return null;
    }

    @Override
    public List<ZdFormAiWorkflowInfoDTO> findConfigByFormCode(String formCode) {
        if (StringUtils.isNotBlank(formCode)) {
            List<ZdAiWorkflowConfig> list = this.dao.find(formCode);
            return list.stream().map(ZdAiWorkflowConvertUtil::toFormAiWorkflowDTO)
                .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public ZdAiWorkflowConfigInnerDTO getBindInfo(Long id) {
        ZdAiWorkflowConfig config = this.dao.getById(id);
        if (Objects.nonNull(config)) {
            return ZdAiWorkflowConvertUtil.toInnerDTO(config);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindAiWorkFlow(ZdAiWorkflowBindParamDTO param) {
        String appId = param.getAppId();

        ZdAiWorkflowBindParamPO po = ZdAiWorkflowConvertUtil.toPO(param);
        Long id = this.dao.bind(po);

        Integer apiKeyProcessType = param.getApiKeyProcessType();
        String apiKey = param.getApiKey();
        if (Objects.equals(apiKeyProcessType, ZdAiWorkflowApiKeyProcessType.MANUAL_INPUT)) {
            if (StringUtils.isNotBlank(apiKey)) {
                String encrypt = AiWorkflowApiKeyUtil.encryptApiKey(apiKey);
                this.dao.updateApiKey(id, encrypt);
            } else {
                log.error("手动输入apiKey时,apiKey不能为空,id: {}", id);
                throw new BussinessException(API_KEY_CANT_BLANK);
            }
        } else if (Objects.equals(apiKeyProcessType, ZdAiWorkflowApiKeyProcessType.AUTO_GENERATE)) {
            String apiKeyName = this.properties.getApiKeyName();
            Long apiKeyMaxUsagePoint = this.properties.getApiKeyMaxUsagePoint();

            CreateApiKeyRequestDTO requestParam = new CreateApiKeyRequestDTO();
            requestParam.setAppId(appId);
            requestParam.setName(apiKeyName);
            requestParam.setLimit(LimitDTO.builder().maxUsagePoints(apiKeyMaxUsagePoint).build());
            ApiResponse<String> response = this.mindApiRemoteService.createApiKey(requestParam);

            if (response.isSuccess()) {
                if (log.isDebugEnabled()) {
                    log.debug("创建应用api key，参数: {},请求参数: {}, 返回值: {}",
                        JsonUtil.toJsonString(param),
                        JsonUtil.toJsonString(requestParam),
                        JsonUtil.toJsonString(response));
                }
                String apiKeyInResponse = response.getData();
                if (StringUtils.isNotBlank(apiKeyInResponse)) {
                    String encrypt = AiWorkflowApiKeyUtil.encryptApiKey(apiKeyInResponse);
                    this.dao.updateApiKey(id, encrypt);
                } else {
                    if (log.isErrorEnabled()) {
                        log.error("创建apiKey时,返回apiKey不能为空,id: {}",
                            JsonUtil.toJsonString(response));
                    }
                    throw new BussinessException(CREATE_API_KEY_FAILED, "返回值为空");
                }
            } else {
                if (log.isErrorEnabled()) {
                    log.error("创建应用api key失败，参数: {},请求参数: {}, 返回值: {}",
                        JsonUtil.toJsonString(param),
                        JsonUtil.toJsonString(requestParam),
                        JsonUtil.toJsonString(response));
                }
                String message = response.getMessage();
                throw new BussinessException(CREATE_API_KEY_FAILED, message);
            }
        } else {
            ZdAiWorkflowConfig data = this.dao.getById(id);
            String apiKeyInDB = data.getApiKey();
            if (StringUtils.isNotBlank(apiKeyInDB)) {
                log.info("ai工作流绑定继续沿用已存在key");
            } else {
                log.info("id {} ai工作流绑定继续沿用已存在key,但是没有找到已存在key", id);
                throw new BussinessException(API_KEY_IS_EMPTY);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByFormCodes(Collection<String> formCodes) {
        if (CollUtil.isNotEmpty(formCodes)) {
            this.dao.deleteByFormCodes(formCodes);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByAppId(String appId) {
        this.dao.deleteByAppId(appId);
    }
}
