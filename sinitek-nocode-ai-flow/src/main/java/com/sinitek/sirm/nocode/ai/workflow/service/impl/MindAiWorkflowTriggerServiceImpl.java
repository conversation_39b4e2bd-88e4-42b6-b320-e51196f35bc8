package com.sinitek.sirm.nocode.ai.workflow.service.impl;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowConstant.DEFAULT_EXE_THREAD_POOL_NAME;
import static com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowResponseWriteUtil.writeSseCompleteRes;

import com.sinitek.sirm.nocode.ai.mind.properties.ZdMindProperties;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowConfigInnerDTO;
import com.sinitek.sirm.nocode.ai.workflow.dto.ZdAiWorkflowTriggerParamDTO;
import com.sinitek.sirm.nocode.ai.workflow.properties.ZdAiWorkflowProperties;
import com.sinitek.sirm.nocode.ai.workflow.service.IMindAiWorkflowTriggerService;
import com.sinitek.sirm.nocode.ai.workflow.service.IZdAiWorkflowService;
import com.sinitek.sirm.nocode.ai.workflow.support.AiWorkflowReqeustRunnable;
import com.sinitek.sirm.nocode.ai.workflow.util.AiWorkflowApiKeyUtil;
import com.sinitek.sirm.nocode.form.service.IZdPageFormInstanceSchemaService;
import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.Executor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * <AUTHOR>
 * @date 2025-08-11 15:55
 */
@Slf4j
@Service
public class MindAiWorkflowTriggerServiceImpl implements IMindAiWorkflowTriggerService {

    @Autowired(required = false)
    private IZdPageFormInstanceSchemaService zdPageFormInstanceSchemaService;

    @Autowired
    private ZdAiWorkflowProperties properties;

    @Autowired
    private ZdMindProperties mindProperties;

    @Autowired
    private IZdAiWorkflowService aiWorkflowService;

    @Autowired
    @Qualifier(DEFAULT_EXE_THREAD_POOL_NAME)
    private Executor executor;

    @Override
    public SseEmitter trigger(ZdAiWorkflowTriggerParamDTO param) {
        Long bindId = param.getBindId();
        String formCode = param.getFormCode();
        String operatorId = param.getOperatorId();

        ZdAiWorkflowConfigInnerDTO bindInfo = this.aiWorkflowService.getBindInfo(bindId);
        Integer sseTimeout = this.properties.getSseTimeout();
        SseEmitter sseEmitter = new SseEmitter(Duration.ofSeconds(sseTimeout).toMillis());
        if (Objects.nonNull(bindInfo)) {
            log.info("触发工作流,表单编码: {},bindId: {},操作人: {}", formCode, bindId,
                operatorId);

            String encryptApikey = bindInfo.getEncryptApikey();
            String apiKey = AiWorkflowApiKeyUtil.decryptApiKey(encryptApikey);
            String eventType = bindInfo.getEventType();
            AiWorkflowReqeustRunnable runnable = new AiWorkflowReqeustRunnable(
                zdPageFormInstanceSchemaService, properties,
                mindProperties, param, sseEmitter, apiKey, eventType);
            this.executor.execute(runnable);
        } else {
            log.info("无须触发工作流,表单编码: {},bindId: {},操作人: {}", formCode, bindId,
                operatorId);
            writeSseCompleteRes(sseEmitter);
            sseEmitter.complete();
        }
        return sseEmitter;
    }
}
