package com.sinitek.sirm.nocode.ai.workflow.config;

import static com.sinitek.sirm.nocode.ai.workflow.constant.ZdAiWorkflowConstant.DEFAULT_EXE_THREAD_POOL_NAME;

import com.sinitek.sirm.nocode.common.support.thread.ContextThreadCleaner;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 导出任务线程池配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Configuration
public class AiWorkflowSSEExeTaskThreadPoolConfig {

    /**
     * 导入任务线程池
     */
    @Bean(DEFAULT_EXE_THREAD_POOL_NAME)
    public Executor importTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(50);

        // 最大线程数
        executor.setMaxPoolSize(200);

        // 队列容量
        executor.setQueueCapacity(100);

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);

        // 线程名前缀
        executor.setThreadNamePrefix("ai-work-flow-sse-exe-task-");

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);

        // 自定义线程工厂，添加线程上下文清理
        executor.setTaskDecorator(runnable -> () -> {
            try {
                runnable.run();
            } finally {
                // 清理线程上下文
                ContextThreadCleaner.cleanAll();
            }
        });

        executor.initialize();

        log.info(
            "ai-work-flow SSE 执行任务线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
            executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }
}
