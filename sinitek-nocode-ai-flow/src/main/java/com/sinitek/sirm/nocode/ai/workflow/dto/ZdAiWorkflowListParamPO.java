package com.sinitek.sirm.nocode.ai.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-08-12 16:37
 */
@Data
@NoArgsConstructor
@ApiModel("智搭ai工作流List查询参数")
public class ZdAiWorkflowListParamPO {

    @NotNull(message = "表单编码不能为空")
    @ApiModelProperty("表单编码")
    private String formCode;

}
