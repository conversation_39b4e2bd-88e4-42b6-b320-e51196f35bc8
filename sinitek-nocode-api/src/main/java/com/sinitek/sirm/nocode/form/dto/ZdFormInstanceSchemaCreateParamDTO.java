package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-15 13:21
 */
@Data
@ApiModel("智搭页面实例Schema创建参数")
public class ZdFormInstanceSchemaCreateParamDTO {

    @NotBlank(message = "页面schema不能为空")
    @ApiModelProperty("页面schema(base64编码)")
    private String schema;

}
