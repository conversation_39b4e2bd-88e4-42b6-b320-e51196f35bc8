package com.sinitek.sirm.nocode.form.service;

import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCacheDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaCreateParamDTO;
import com.sinitek.sirm.nocode.form.dto.ZdFormInstanceSchemaUpdateParamDTO;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-15 13:20
 */
public interface IZdPageFormInstanceSchemaService {

    String createSchemaCache(ZdFormInstanceSchemaCreateParamDTO param);

    void deleteSchemaCache(String schemaId);

    void deleteSchemaCacheBatch(Collection<String> schemaIds);

    void updateSchemaCache(ZdFormInstanceSchemaUpdateParamDTO param);

    ZdFormInstanceSchemaCacheDTO getSchemaCache(String schemaId);

    List<String> findCachedSchemaId();
}
