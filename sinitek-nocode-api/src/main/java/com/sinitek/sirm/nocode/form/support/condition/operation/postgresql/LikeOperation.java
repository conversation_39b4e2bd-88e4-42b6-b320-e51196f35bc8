package com.sinitek.sirm.nocode.form.support.condition.operation.postgresql;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.sirm.nocode.form.constant.ZdPgSqlConstant;
import com.sinitek.sirm.nocode.form.enumerate.OperatorEnum;
import com.sinitek.sirm.nocode.form.support.condition.OperationInterface;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 2025.0613
 * @since 1.0.0-SNAPSHOT
 */

public class LikeOperation implements OperationInterface {
    @Override
    public void apply(QueryWrapper<?> wrapper, String fieldName, Object value) {
        String like = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_LIKE_SUBMIT_FIELD, fieldName, value);
        if (Objects.equals(fieldName, ZdPgSqlConstant.ARRAY_ALL_VALUE)) {
            // 全文检索
            String displayValue = String.format(ZdPgSqlConstant.JSONB_PATH_EXISTS_LIKE_SUBMIT_FIELD, ZdPgSqlConstant.ARRAY_ALL_DISPLAY_VALUE, value);
            wrapper.and(q -> q.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS_SUBMIT_FIELD, like)
                    .or()
                    .apply(ZdPgSqlConstant.JSONB_PATH_EXISTS_SUBMIT_FIELD, displayValue));

        } else {
            wrapper.apply(ZdPgSqlConstant.JSONB_PATH_EXISTS_SUBMIT_FIELD, like);
        }

    }

    @Override
    public OperatorEnum type() {
        return OperatorEnum.LIKE;
    }
}
