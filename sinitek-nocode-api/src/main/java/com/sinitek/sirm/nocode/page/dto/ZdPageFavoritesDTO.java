package com.sinitek.sirm.nocode.page.dto;

import com.sinitek.sirm.nocode.common.dto.base.BaseEntityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 页面收藏DTO
 *
 * <AUTHOR>
 * @version 2025.0814
 * @since 1.0.0-SNAPSHOT
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "页面收藏DTO")
public class ZdPageFavoritesDTO extends BaseEntityDTO {

    /**
     * 页面编码
     */
    @NotBlank(message = "页面编码不能为空")
    @ApiModelProperty(value = "页面编码", example = "page_23dcc69621b342f4b46a33473c25f6b9", required = true)
    private String formCode;

    /**
     * 收藏人员id
     */
    @NotBlank(message = "收藏人员id不能为空")
    @ApiModelProperty(value = "收藏人员id", example = "999000001", required = true)
    private String orgId;
}
