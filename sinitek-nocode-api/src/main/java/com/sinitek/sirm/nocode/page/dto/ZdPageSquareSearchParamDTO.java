package com.sinitek.sirm.nocode.page.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinitek.sirm.nocode.common.dto.ZdSearchParamDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2025.0711
 * @since 1.0.0-SNAPSHOT
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "广场查询参数")
public class ZdPageSquareSearchParamDTO extends ZdSearchParamDTO {
    @ApiModelProperty(value = "类型，0:表单，2:AI问答页，null:全部", example = "0")
    private Integer type;

    @ApiModelProperty("是否收藏")
    private Boolean favoritesFlag;

    @JsonIgnore
    @ApiModelProperty(value = "当前登陆人orgId", required = true, example = "999999001")
    private String currentOrgId;
}
