package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-08-15 13:21
 */
@Data
@ApiModel("智搭页面实例Schema变动参数")
public class ZdFormInstanceSchemaDiffComponentItemDTO {

    @ApiModelProperty("变动类型")
    private String type;

    @ApiModelProperty("组件数据")
    private Map<String, Object> data;

}
