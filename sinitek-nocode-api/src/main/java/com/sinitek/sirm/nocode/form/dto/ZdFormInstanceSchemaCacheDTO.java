package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-08-15 13:21
 */
@Data
@ApiModel("智搭页面实例Schema缓存DTO")
public class ZdFormInstanceSchemaCacheDTO {

    @ApiModelProperty("schemaId")
    private String schemaId;

    @ApiModelProperty("页面实例schema")
    private Map<String, Object> schema;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
