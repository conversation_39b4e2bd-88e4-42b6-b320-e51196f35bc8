package com.sinitek.sirm.nocode.form.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-08-15 13:21
 */
@Data
@ApiModel("智搭页面实例Schema更新参数")
public class ZdFormInstanceSchemaUpdateParamDTO {

    @NotBlank(message = "页面schemaId不能为空")
    @ApiModelProperty("页面schemaId")
    private String schemaId;

    @NotBlank(message = "页面schema差异数据不能为空")
    @ApiModelProperty("页面schema差异数据(base64编码)")
    private String diff;

}
