package com.sinitek.sirm.nocode.page.service;

import com.sinitek.sirm.nocode.page.dto.ZdPageFavoritesDTO;

/**
 * 页面收藏服务接口
 *
 * <AUTHOR>
 * @version 2025.0719
 * @since 1.0.0-SNAPSHOT
 */
public interface IZdPageFavoritesService {
    /**
     * 保存或更新页面收藏
     *
     * @param dto 页面收藏DTO
     * @return 保存结果
     */
    boolean saveOrUpdate(ZdPageFavoritesDTO dto);

    /**
     * 删除页面收藏
     *
     * @param pageCode 页面编码
     * @param orgId    组织ID
     * @return 删除结果
     */
    boolean delete(String pageCode, String orgId);

}
