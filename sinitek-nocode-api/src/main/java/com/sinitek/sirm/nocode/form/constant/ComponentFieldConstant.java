package com.sinitek.sirm.nocode.form.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 字段属性
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class ComponentFieldConstant {

    public static final String ID = "id";
    public static final String PROPS = "props";
    public static final String REF = "ref";
    public static final String COMPONENT_NAME = "componentName";
    public static final String VALUE = "value";
    public static final String CHILDREN = "children";

    public static final String LABEL_IN_PROPS = "label";
    public static final String OPTIONS_IN_PROPS = "options";
    public static final String FORMATTER_IN_PROPS = "formatter";

    public static final String RULES_IN_PROPS = "rules";
    public static final String RULE_KEY = "key";
    public static final String REQUIRED_RULE_KEY = "required";
    public static final String ENABLE_RULE_KEY = "enable";

    public static final String MULTIPLE_IN_PROPS = "multiple";

    public static final String LABEL_IN_OPTIONS = "label";
    public static final String VALUE_IN_OPTIONS = "value";
    public static final String OTHER_VALUE = "_other_";
    public static final String OTHER_LABEL = "其他";

    public static final String ASSOCIATION_DISPLAY_OPTION_KEY = "displayOption";
    public static final String ASSOCIATION_PRIMARY_INFO_KEY = "primaryInfo";
    public static final String ASSOCIATION_SECONDARY_INFO_KEY = "secondaryInfo";
    public static final String ASSOCIATION_DISPLAY_OPTION_REF_KEY = "ref";

    public static final String ASSOCIATION_FORM_KEY = "associationForm";
    public static final String ASSOCIATION_FORM_APP_CODE_KEY = "appCode";
    public static final String ASSOCIATION_FORM_FORM_CODE_KEY = "formCode";

}
