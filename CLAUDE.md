# CLAUDE.md

本文件为Claude Code (claude.ai/code)在此代码仓库中工作时提供指导。

## 项目概述

**Sinitek NoCode Backend** - 企业级零代码平台后端系统，基于Spring Boot的多模块Maven项目。该平台通过PostgreSQL的JSONB字段动态存储表单配置和数据，实现零代码应用构建，并集成LLM功能提供AI辅助。

## 构建和开发命令

### Maven 构建
```bash
# 构建整个项目
mvn clean compile

# 打包应用
mvn clean package

# 运行测试
mvn test

# 跳过测试打包
mvn clean package -DskipTests=true
```

### 应用启动
```bash
# 主启动类: com.sinitek.sirm.nocode.SirmApplication
# 应用端口: 8097
# 上下文路径: /zhida

# 运行Spring Boot应用
java -jar sinitek-nocode-assembly/target/sinitek-nocode-assembly.jar
```

### 测试
```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl sinitek-nocode-assembly

# 运行Spock测试（Groovy）
mvn test -Dtest="**/TestGroovy"
```

## 架构概览

### 多模块结构
```
sinitek-nocode-backend/
├── sinitek-nocode-api/           # API接口层 (DTO、Service接口)
├── sinitek-nocode-dal/           # 数据访问层 (Entity、Mapper、数据库脚本)
├── sinitek-nocode-assembly/      # 应用启动模块 (配置、启动类)
├── sinitek-nocode-service-design/   # 设计时服务 (表单设计、页面配置)
├── sinitek-nocode-service-runtime/  # 运行时服务 (数据操作、权限控制)
├── sinitek-nocode-service-llm/      # LLM服务 (AI辅助功能)
├── sinitek-nocode-common/           # 公共工具和组件
├── sinitek-nocode-constant/         # 常量和枚举定义
└── sinitek-nocode-message/          # 消息处理模块
```

### 核心业务模块
- **App模块**: 应用管理、权限控制、OA集成 (`com.sinitek.sirm.nocode.app.*`)
- **Page模块**: 页面配置、权限设置、场景配置 (`com.sinitek.sirm.nocode.page.*`)
- **Form模块**: 表单设计、数据管理、字段映射 (`com.sinitek.sirm.nocode.form.*`)
- **Common模块**: 通用DTO、工具类、树结构 (`com.sinitek.sirm.nocode.common.*`)

### 技术栈
- **Java 8** 和 Maven 多模块构建
- **Spring Boot 2.x** 和 Spring Cloud 微服务支持
- **MyBatis Plus** ORM 和 PostgreSQL 数据库
- **Nacos** 服务注册和配置中心
- **Redis** 缓存
- **Spock Framework** 基于 Groovy 的测试框架
- **Swagger/OpenAPI** API 文档生成

## 数据库设计模式

### 基于JSON的动态存储
- 使用PostgreSQL JSONB字段存储动态表单配置和数据
- 表单配置存储在 `zd_page_form.page_data: jsonb`
- 表单数据存储在分表 `zd_page_form_data_*.form_data: jsonb`
- 权限配置存储在 `zd_page_auth.field_auth: jsonb`

### 数据库迁移
- Flyway迁移脚本位于 `sinitek-nocode-dal/src/main/resources/db/postgresql/`
- 命名规范: `V100_YYYYMMDD_NN__description.sql`
- 架构变更始终使用 `IF NOT EXISTS` 子句

## 编码规范

### 包结构
- API层: `com.sinitek.sirm.nocode.{module}.dto` 和 `service`
- 数据层: `com.sinitek.sirm.nocode.{module}.entity` 和 `mapper`
- 服务实现: `com.sinitek.sirm.nocode.{module}.service.impl`

### 类命名模式
- DTOs: `Zd{Entity}DTO` 继承 `ZdIdDTO`
- 实体类: `Zd{Entity}` 继承 `BaseAuditEntity`
- 服务类: `IZd{Entity}Service` (接口), `Zd{Entity}ServiceImpl` (实现类)
- 映射器: `Zd{Entity}Mapper` 配对相应的XML文件

### 关键注解
- `@Validated` 配合分组验证 (`ZdIdDTO.Save.class`)
- `@ApiModel` 和 `@ApiModelProperty` 用于Swagger文档
- `@Transactional` 用于服务层事务管理
- `@TableName` 用于MyBatis Plus实体映射

### 测试模式
- 继承 `ZdBeanUnitTest` 进行Spring Boot集成测试
- 使用Spock框架 (`TestGroovy.groovy`) 进行行为驱动测试
- 测试配置: `@ActiveProfiles("unit-test")` 或 `@ActiveProfiles("dev")`

## 配置管理

### 应用配置文件
- `application.yml` - 主配置文件
- `bootstrap-local.yml` - 本地开发环境
- `bootstrap-test.yml` - 测试环境
- `bootstrap.yml` - 引导配置

### 关键配置
- 服务端口: 8097
- 上下文路径: `/zhida`
- MyBatis Plus 枚举包: `com.sinitek.sirm.nocode.*.enumerate`
- 数据库ID类型: `ASSIGN_ID` (雪花算法)

## 开发指南

### JSON字段操作
```java
// 使用MyBatis Plus JSON处理
@TableField(typeHandler = JacksonTypeHandler.class)
private Map<String, Object> formData;

// PostgreSQL JSON查询
formData->'fieldName' = 'value'
formData @> '{"status": 1}'
```

### 分表模式
```java
// 实现ITableName接口实现动态表名
public class ZdPageFormDataDTO implements ITableName {
    @Override
    public String getTableName() {
        return "zd_page_form_data_" + getTableSuffix();
    }
}
```

### 错误处理
- API响应使用 `RequestResult<T>`
- 业务异常继承框架基础类
- 国际化消息存储在 `messages-nocode_zh_CN.properties`

## AI/LLM 集成

### LLM 服务模块
- 位置: `sinitek-nocode-service-llm`
- 功能: 智能表单生成、SQL生成、图表生成
- API 端点: `/frontend/api/nocode/llm/*`

### 配置
```yaml
llm:
  sinitek-chat:
    server: http://192.168.22.247
    api-key: app-xxxxx
```

## 重要规则

### 项目开发规范（基于.cursor/rules配置）
1. **技术栈版本不可变更** - 严格按照现有技术栈版本进行开发
2. **UI/UX设计变更需批准** - 布局、颜色、字体等变更需要事先获得批准
3. **防止重复实现** - 开发前检查现有功能、API和组件
4. **遵循命名规范** - 表名`zd_`前缀，字段小写下划线，应用编码`app_`前缀

### Git提交规范
|前缀|备注|
|:---:|:----|
|rq|需求 - 需求名称+需求编号|
|bug|BUG - BUG编号+BUG描述|
|ft|特性功能|
|doc|文档修改|
|spec|规范调整|

### SQL开发规范
- 新增迁移文件到 `db/postgresql/` 目录
- 使用 `IF NOT EXISTS` 条件语句
- 禁止 `DROP TABLE` 语句