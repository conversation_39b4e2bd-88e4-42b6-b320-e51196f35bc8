package com.sinitek.sirm.nocode.common.properties;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-07-21 17:24
 */
@Component
@ConfigurationProperties(
    prefix = "nocode.global"
)
@Data
@ApiModel(description = "低代码全局配置")
public class ZdGlobalProperties {

    @ApiModelProperty(value = "临时文件路径", required = true, example = "/tmp")
    private String tempDir = "/tmp";

    @ApiModelProperty(value = "平台管理员OrgId(只有系统参数缺失时才使用该配置)")
    private String adminOrgId;

    @ApiModelProperty(value = "临时文件后缀名")
    private String tempFileExtention = "dat";

    @ApiModelProperty(value = "单线程下载容量")
    private Integer singleThreadDownloadSize = 100;

    @ApiModelProperty(value = "下载超时时间(毫秒)")
    private Integer downloadTimeout = 60 * 1000;

    @ApiModelProperty(value = "帮助文档地址")
    private String helpDocUrl = "http://*************:18056/doc/sinitek-zhida/";
}
