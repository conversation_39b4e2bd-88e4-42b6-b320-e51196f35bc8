package com.sinitek.sirm.nocode.form.support.handler.delete;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.sinitek.sirm.common.attachment.service.IAttachmentExtService;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.nocode.form.entity.ZdPageFormData;
import com.sinitek.sirm.nocode.form.enumerate.PageDataComponentTypeEnum;
import com.sinitek.sirm.nocode.form.support.handler.base.DeleteDataHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 附件删除处理器
 *
 * <AUTHOR>
 * @version 2025.0707
 * @since 1.0.0-SNAPSHOT
 */
@Component
public class DeleteUploadFileHandler implements DeleteDataHandler {

    @Resource
    private IAttachmentExtService attachmentExtService;

    @Override
    public void handler(List<ZdPageFormData> formData, boolean deleteAll) {
        formData.forEach(zdPageFormData -> {
            Map<String, Object> formDataMap = zdPageFormData.getFormDataMap();
            deleteUploadFile(formDataMap, zdPageFormData);
        });
    }

    @SuppressWarnings("unchecked")
    private void deleteUploadFile(Map<String, Object> formDataMap, ZdPageFormData formData) {
        formDataMap.forEach((key, value) -> {
            PageDataComponentTypeEnum pageDataComponentTypeEnum = PageDataComponentTypeEnum.formKey(key);
            if (PageDataComponentTypeEnum.ZD_UPLOAD.equals(pageDataComponentTypeEnum)) {
                if (!(value instanceof Map)) {
                    return;
                }
                Map<String, Object> uploadFileMap = (Map<String, Object>) value;
                String sourceEntity = formData.getFormCode();
                Object sourceIdStr = uploadFileMap.get("sourceId");
                Long sourceId = NumberTool.safeToLong(sourceIdStr, 0L);
                // 删除上传文件
                attachmentExtService.removeAttachment(sourceEntity, sourceId);
            } else if (PageDataComponentTypeEnum.ZD_CHILD_FORM.equals(pageDataComponentTypeEnum) && (value instanceof List)) {
                List<Map<String, Object>> childFormDataList = (List<Map<String, Object>>) value;
                if (CollectionUtils.isNotEmpty(childFormDataList)) {
                    childFormDataList.forEach(childFormData -> deleteUploadFile(childFormData, formData));
                }
            }
        });
    }
}
