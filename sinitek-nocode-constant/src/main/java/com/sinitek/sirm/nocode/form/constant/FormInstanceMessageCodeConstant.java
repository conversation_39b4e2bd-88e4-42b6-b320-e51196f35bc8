package com.sinitek.sirm.nocode.form.constant;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class FormInstanceMessageCodeConstant {

    /**
     * 3000025001=页面Schema缓存创建失败
     */
    public static final String SCHEMA_CACHE_CREATE_FAILED = "3000025001";

    /**
     * 3000025002=页面实例Schema缓存更新失败
     */
    public static final String SCHEMA_CACHE_UPDATE_FAILED = "3000025002";


}
